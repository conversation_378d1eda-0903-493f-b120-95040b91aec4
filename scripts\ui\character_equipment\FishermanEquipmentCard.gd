# FishermanEquipmentCard.gd
# 渔夫装备卡片 - 配置驱动的渔夫装备管理
class_name FishermanEquipmentCard
extends CharacterEquipmentCard

## 配置驱动常量 ##
const EXPANDED_HEIGHT = GameConstants.UIConstants.FISHERMAN_CARD_EXPANDED_HEIGHT
const COLLAPSED_HEIGHT = GameConstants.UIConstants.FISHERMAN_CARD_COLLAPSED_HEIGHT

## 渔夫特有节点引用 ##
@onready var expand_button: Button = $MainVBox/MainHBox/ExpandButton
@onready var catch_display_container: VBoxContainer = $MainVBox/CatchDisplayContainer
@onready var junk_grid: GridContainer = $MainVBox/CatchDisplayContainer/ScrollContainer/CatchCategoriesVBox/JunkCategory/JunkGrid
@onready var freshwater_grid: GridContainer = $MainVBox/CatchDisplayContainer/ScrollContainer/CatchCategoriesVBox/FreshwaterCategory/FreshwaterGrid
@onready var saltwater_grid: GridContainer = $MainVBox/CatchDisplayContainer/ScrollContainer/CatchCategoriesVBox/SaltwaterCategory/SaltwaterGrid

## 状态变量 ##
var is_expanded: bool = false
var fishing_data_manager: FishingDataManager = null
var is_updating_catch_display: bool = false
var _last_equipment_combination: String = ""  # 缓存上次的装备组合

## 生命周期 ##
func _ready() -> void:
    # 不调用super._ready()，因为父类的@onready引用会失败
    # 直接复制父类_ready的逻辑，但使用正确的节点引用
    var card_height = COLLAPSED_HEIGHT
    custom_minimum_size = Vector2(0, card_height)
    size_flags_horizontal = Control.SIZE_EXPAND_FILL
    
    _initialize_styles()
    _setup_equipment_slots()
    _connect_signals()
    
    # 渔夫特有的初始化
    expand_button.pressed.connect(_on_expand_button_pressed)
    _initialize_fishing_data_manager()

func _initialize_fishing_data_manager() -> void:
    """初始化渔业数据管理器 - 配置驱动模式"""
    var game_manager = _get_game_manager_reference()
    if game_manager and game_manager.has_method("get_fishing_data_manager"):
        fishing_data_manager = game_manager.get_fishing_data_manager()
    else:
        # 回退：创建本地实例
        fishing_data_manager = FishingDataManager.new()
        add_child(fishing_data_manager)
        fishing_data_manager.load_fishing_data()

    # 连接信号
    if is_instance_valid(fishing_data_manager) and not fishing_data_manager.fishing_data_loaded.is_connected(_on_fishing_data_loaded):
        fishing_data_manager.fishing_data_loaded.connect(_on_fishing_data_loaded)

func _get_game_manager_reference():
    """获取GameManager引用 - 标准模式"""
    var game_manager = get_node_or_null("/root/_GameManager")
    return game_manager if is_instance_valid(game_manager) else null

func _on_fishing_data_loaded() -> void:
    """渔业数据加载完成"""
    if is_expanded:
        _update_catch_display()

func _on_expand_button_pressed() -> void:
    """展开按钮点击"""
    is_expanded = not is_expanded
    catch_display_container.visible = is_expanded
    expand_button.text = "▲" if is_expanded else "▼"
    
    # 调整卡片高度
    if is_expanded:
        custom_minimum_size.y = EXPANDED_HEIGHT
        _update_catch_display()
    else:
        custom_minimum_size.y = COLLAPSED_HEIGHT
    
    # 强制更新布局
    queue_redraw()

## 重写父类方法 ##
func setup_character(char_id: String, char_type: String, char_name: String, eq_manager: EquipmentManager) -> void:
    """设置角色信息"""
    if char_id.is_empty() or char_name.is_empty() or not eq_manager:
        return
    
    character_id = char_id
    character_type = char_type
    character_name = char_name
    equipment_manager = eq_manager
    
    # 更新UI显示 - 使用正确的节点路径
    var name_label_node = $MainVBox/MainHBox/InfoVBox/NameLabel as Label
    var avatar_container_node = $MainVBox/MainHBox/AvatarContainer as Panel
    var avatar_icon_node = $MainVBox/MainHBox/AvatarContainer/AvatarIcon as TextureRect
    var _equipment_container_node = $MainVBox/MainHBox/EquipmentContainer as HBoxContainer
    
    name_label_node.text = character_name
    _update_avatar_custom(avatar_container_node, avatar_icon_node)
    _update_equipment_display()
    
    # 更新装备槽位的角色ID
    for slot in equipment_slots.values():
        slot.character_id = character_id
        # 如果是渔夫装备槽位，重新连接信号
        if slot is FishermanEquipmentSlot:
            slot.reconnect_fisherman_signals()
    
    # 如果数据已加载且已展开，立即更新钓获展示
    if is_instance_valid(fishing_data_manager) and fishing_data_manager.is_loaded and is_expanded:
        _update_catch_display()

func _setup_equipment_slots() -> void:
    """设置装备槽位组件 - 渔夫使用特殊槽位"""
    var slot_panels = {
        "main_hand": $MainVBox/MainHBox/EquipmentContainer/MainHandSlot,
        "off_hand": $MainVBox/MainHBox/EquipmentContainer/OffHandSlot,
        "accessory": $MainVBox/MainHBox/EquipmentContainer/AccessorySlot,
        "consumable": $MainVBox/MainHBox/EquipmentContainer/ConsumableSlot
    }
    
    for slot_name in slot_panels:
        var slot_panel = slot_panels[slot_name]
        var slot_scene_path = "res://scenes/UI/FishermanEquipmentSlot.tscn" if slot_name in ["main_hand", "off_hand"] else "res://scenes/UI/EquipmentSlot.tscn"
        var slot = load(slot_scene_path).instantiate()
        
        slot.slot_size = Vector2(SLOT_SIZE, SLOT_SIZE)
        slot.setup_slot(character_id, slot_name)
        slot.slot_clicked.connect(_on_equipment_slot_clicked)
        
        slot_panel.add_child(slot)
        slot.anchors_preset = Control.PRESET_FULL_RECT
        equipment_slots[slot_name] = slot

func _on_equipment_slot_clicked(slot_name: String, current_item_id: String) -> void:
    """装备槽位点击事件"""
    equipment_slot_clicked.emit(character_id, slot_name, current_item_id)

func refresh_display() -> void:
    """刷新显示"""
    _update_equipment_display()
    if is_expanded:
        _update_catch_display()

func force_refresh_catch_display() -> void:
    """强制刷新钓获显示（无论是否展开）"""
    if is_instance_valid(fishing_data_manager) and fishing_data_manager.is_loaded:
        _update_catch_display()
    else:
        # 延迟刷新，等待数据管理器准备好
        call_deferred("_delayed_catch_display_refresh")

func _delayed_catch_display_refresh() -> void:
    """延迟的钓获显示刷新"""
    if is_instance_valid(fishing_data_manager) and fishing_data_manager.is_loaded:
        _update_catch_display()

## 自定义头像更新方法 ##
func _update_avatar_custom(avatar_container_node: Panel, avatar_icon_node: TextureRect) -> void:
    """更新头像显示 - 使用传入的节点"""
    # 根据角色类型设置头像
    var avatar_texture = _get_character_avatar_texture()
    if avatar_texture:
        avatar_icon_node.texture = avatar_texture
    
    # 设置头像容器样式
    var avatar_style = StyleBoxFlat.new()
    avatar_style.bg_color = _get_character_type_color()
    avatar_style.corner_radius_top_left = 4
    avatar_style.corner_radius_top_right = 4
    avatar_style.corner_radius_bottom_left = 4
    avatar_style.corner_radius_bottom_right = 4
    avatar_container_node.add_theme_stylebox_override("panel", avatar_style)

func _on_gui_input(event: InputEvent) -> void:
    """处理卡片点击"""
    if event is InputEventMouseButton and event.pressed:
        # 检查是否点击在装备槽位上
        var local_pos = event.position
        var equipment_rect = ($MainVBox/MainHBox/EquipmentContainer as HBoxContainer).get_rect()
        
        if not equipment_rect.has_point(local_pos):
            # 点击在非装备区域
            if event.button_index == MOUSE_BUTTON_LEFT:
                # 左键点击 - 选择角色
                character_selected.emit(character_id)
            elif event.button_index == MOUSE_BUTTON_RIGHT:
                # 右键点击 - 请求删除角色
                character_delete_requested.emit(character_id)

## 从父类复制的必要方法 ##
func _initialize_styles() -> void:
    """初始化样式"""
    # 普通样式
    _normal_style = StyleBoxFlat.new()
    _normal_style.bg_color = Color(0.25, 0.25, 0.25, 0.8)
    _normal_style.border_width_left = 1
    _normal_style.border_width_top = 1
    _normal_style.border_width_right = 1
    _normal_style.border_width_bottom = 1
    _normal_style.border_color = Color(0.4, 0.4, 0.4, 0.6)
    _normal_style.corner_radius_top_left = 4
    _normal_style.corner_radius_top_right = 4
    _normal_style.corner_radius_bottom_left = 4
    _normal_style.corner_radius_bottom_right = 4
    
    # 选中样式
    _selected_style = StyleBoxFlat.new()
    _selected_style.bg_color = Color(0.3, 0.4, 0.6, 0.9)
    _selected_style.border_width_left = 2
    _selected_style.border_width_top = 2
    _selected_style.border_width_right = 2
    _selected_style.border_width_bottom = 2
    _selected_style.border_color = Color(0.5, 0.7, 1.0, 1.0)
    _selected_style.corner_radius_top_left = 4
    _selected_style.corner_radius_top_right = 4
    _selected_style.corner_radius_bottom_left = 4
    _selected_style.corner_radius_bottom_right = 4

func _connect_signals() -> void:
    """连接信号"""
    if not gui_input.is_connected(_on_gui_input):
        gui_input.connect(_on_gui_input)

func set_selected(selected: bool) -> void:
    """设置选中状态"""
    is_selected = selected
    
    if selected:
        add_theme_stylebox_override("panel", _selected_style)
    else:
        add_theme_stylebox_override("panel", _normal_style)

func _get_character_avatar_texture() -> Texture2D:
    """获取角色头像纹理"""
    var base_texture = load("res://assets/textures/Icons.png") as Texture2D
    if not base_texture:
        return null
    
    # 为不同角色类型使用不同的图标区域
    var atlas_texture = AtlasTexture.new()
    atlas_texture.atlas = base_texture
    
    match character_type:
        "farmer":
            atlas_texture.region = Rect2(0, 0, 16, 16)
        "woodcutter":
            atlas_texture.region = Rect2(16, 0, 16, 16)
        "fisherman":
            atlas_texture.region = Rect2(32, 0, 16, 16)
        "chef":
            atlas_texture.region = Rect2(48, 0, 16, 16)
        _:
            atlas_texture.region = Rect2(0, 0, 16, 16)
    
    return atlas_texture

func _get_character_type_color() -> Color:
    """获取角色类型对应的颜色"""
    match character_type:
        "farmer":
            return Color(0.3, 0.7, 0.3, 0.8)  # 绿色
        "woodcutter":
            return Color(0.6, 0.4, 0.2, 0.8)  # 棕色
        "fisherman":
            return Color(0.2, 0.5, 0.8, 0.8)  # 蓝色
        "chef":
            return Color(0.8, 0.4, 0.2, 0.8)  # 橙色
        _:
            return Color(0.5, 0.5, 0.5, 0.8)  # 灰色

func _update_equipment_display() -> void:
    """更新装备显示"""
    if not equipment_manager:
        return
    
    var char_equipment = equipment_manager.get_character_equipment(character_id)
    
    for slot_name in equipment_slots:
        var slot = equipment_slots[slot_name]
        if is_instance_valid(slot):
            var item_id = char_equipment.get(slot_name, "")
            slot.set_equipment(item_id, equipment_manager)

## 钓获展示功能 - 简化版本 ##
func _update_catch_display() -> void:
    """更新钓获展示 - 带缓存优化"""
    if is_updating_catch_display or not _is_fishing_data_ready():
        return

    var rod_id = _get_equipped_item("main_hand")
    var bait_id = _get_equipped_item("off_hand")
    var current_combination = "%s_%s" % [rod_id, bait_id]

    # 如果装备组合没有变化，跳过更新
    if current_combination == _last_equipment_combination:
        return

    is_updating_catch_display = true
    _last_equipment_combination = current_combination

    if rod_id.is_empty() or bait_id.is_empty():
        _clear_all_catch_displays()
    else:
        _update_all_category_displays(rod_id, bait_id)

    is_updating_catch_display = false

func _is_fishing_data_ready() -> bool:
    """检查渔业数据是否准备就绪"""
    return is_instance_valid(fishing_data_manager) and fishing_data_manager.is_loaded

func _get_equipped_item(slot_name: String) -> String:
    """获取装备槽位的物品ID"""
    if not equipment_manager:
        return ""
    var char_equipment = equipment_manager.get_character_equipment(character_id)
    return char_equipment.get(slot_name, "")

func _update_all_category_displays(rod_id: String, bait_id: String) -> void:
    """更新所有分类的钓获展示"""
    var categories = [
        {"name": "junk", "grid": junk_grid},
        {"name": "freshwater_fish", "grid": freshwater_grid},
        {"name": "saltwater_fish", "grid": saltwater_grid}
    ]

    for category_info in categories:
        _update_category_catch_display(category_info.name, rod_id, bait_id, category_info.grid)

func _update_category_catch_display(category: String, rod_id: String, bait_id: String, grid: GridContainer) -> void:
    """更新指定分类的钓获展示"""
    if not is_instance_valid(grid):
        return

    # 清空现有内容
    _clear_grid_children(grid)

    # 获取钓获数据
    var water_type = "saltwater" if category == "saltwater_fish" else "freshwater"
    var available_catches = fishing_data_manager.get_available_catches(rod_id, bait_id, water_type)
    var probabilities = fishing_data_manager.calculate_fishing_probabilities(rod_id, bait_id, water_type)

    # 创建物品显示
    for catch_info in available_catches:
        if catch_info.category == category:
            _create_catch_item_display(grid, catch_info, probabilities)

func _clear_grid_children(grid: GridContainer) -> void:
    """清空网格子节点"""
    for child in grid.get_children():
        grid.remove_child(child)
        child.queue_free()

func _create_catch_item_display(parent: GridContainer, catch_info: Dictionary, probabilities: Dictionary) -> void:
    """创建钓获物品显示"""
    var item_panel = Panel.new()
    item_panel.custom_minimum_size = Vector2(32, 32)
    item_panel.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
    item_panel.size_flags_vertical = Control.SIZE_SHRINK_CENTER
    
    # 设置背景样式
    var tier = catch_info.data.get("tier", 0)
    var bg_style = _create_tier_style(tier)
    item_panel.add_theme_stylebox_override("panel", bg_style)
    
    # 添加图标
    var icon_rect = _create_item_icon(catch_info)
    item_panel.add_child(icon_rect)
    
    # 添加概率标签
    var prob_label = _create_probability_label(catch_info, probabilities)
    item_panel.add_child(prob_label)
    
    # 设置工具提示
    var item_name = catch_info.data.get("display_name", "未知物品")
    var tier_name = _get_tier_display_name(tier)
    var prob_text = prob_label.text
    item_panel.tooltip_text = "[%s] %s\n概率: %s" % [tier_name, item_name, prob_text]
    
    parent.add_child(item_panel)

func _create_tier_style(tier: int) -> StyleBoxFlat:
    """创建等级背景样式"""
    var bg_style = StyleBoxFlat.new()
    match tier:
        0: bg_style.bg_color = Color(0.5, 0.5, 0.5, 1.0)  # 杂物 - 灰色
        1: bg_style.bg_color = Color(0.8, 0.8, 0.8, 1.0)  # 普通 - 白色
        2: bg_style.bg_color = Color(0.2, 0.8, 0.2, 1.0)  # 优秀 - 绿色
        3: bg_style.bg_color = Color(0.2, 0.4, 1.0, 1.0)  # 稀有 - 蓝色
    
    bg_style.corner_radius_top_left = 4
    bg_style.corner_radius_top_right = 4
    bg_style.corner_radius_bottom_left = 4
    bg_style.corner_radius_bottom_right = 4
    bg_style.border_width_left = 1
    bg_style.border_width_top = 1
    bg_style.border_width_right = 1
    bg_style.border_width_bottom = 1
    bg_style.border_color = Color(0.7, 0.7, 0.7, 1.0)
    
    return bg_style

func _create_item_icon(catch_info: Dictionary) -> TextureRect:
    """创建物品图标"""
    var icon_rect = TextureRect.new()
    icon_rect.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
    icon_rect.offset_left = 3
    icon_rect.offset_top = 3
    icon_rect.offset_right = -3
    icon_rect.offset_bottom = -3
    icon_rect.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
    
    var texture = _get_catch_item_texture(catch_info)
    if texture:
        icon_rect.texture = texture
    else:
        # 备用文字标签
        var label = Label.new()
        label.text = catch_info.id.substr(0, 2).to_upper()
        label.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
        label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
        label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
        label.add_theme_font_size_override("font_size", 8)
        label.add_theme_color_override("font_color", Color.WHITE)
        icon_rect.add_child(label)
    
    return icon_rect

func _create_probability_label(catch_info: Dictionary, probabilities: Dictionary) -> Label:
    """创建概率标签"""
    var prob_label = Label.new()
    prob_label.set_anchors_and_offsets_preset(Control.PRESET_BOTTOM_RIGHT)
    prob_label.offset_left = -20
    prob_label.offset_top = -12
    prob_label.offset_right = -2
    prob_label.offset_bottom = -2
    prob_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
    prob_label.vertical_alignment = VERTICAL_ALIGNMENT_BOTTOM
    
    var full_item_id = catch_info.category + "." + catch_info.id
    var probability = probabilities.get("item_probabilities", {}).get(full_item_id, 0.0)
    prob_label.text = "%.1f%%" % (probability * 100)
    
    prob_label.add_theme_font_size_override("font_size", 8)
    prob_label.add_theme_color_override("font_color", Color.YELLOW)
    prob_label.add_theme_color_override("font_shadow_color", Color.BLACK)
    prob_label.add_theme_constant_override("shadow_offset_x", 1)
    prob_label.add_theme_constant_override("shadow_offset_y", 1)
    
    return prob_label

func _get_catch_item_texture(catch_info: Dictionary) -> Texture2D:
    """获取钓获物品纹理"""
    var visuals = catch_info.data.get("visuals", {})
    if visuals.is_empty():
        return null
    
    var spritesheet_path = visuals.get("spritesheet_path", "")
    if spritesheet_path.is_empty():
        return null
    
    var base_texture = load(spritesheet_path) as Texture2D
    if not base_texture:
        return null
    
    var icon_source = visuals.get("icon_source", {})
    var regions = visuals.get("regions", {})
    var region_key = icon_source.get("path_or_region_key", "")
    
    if region_key.is_empty() or not regions.has(region_key):
        return base_texture
    
    var region_data = regions[region_key]
    var atlas_texture = AtlasTexture.new()
    atlas_texture.atlas = base_texture
    atlas_texture.region = Rect2(
        region_data.get("x", 0),
        region_data.get("y", 0),
        region_data.get("w", 16),
        region_data.get("h", 16)
    )
    
    return atlas_texture

func _get_tier_display_name(tier: int) -> String:
    """获取等级显示名称"""
    match tier:
        0: return "杂物"
        1: return "普通"
        2: return "优秀"
        3: return "稀有"
        _: return "未知"

func _clear_all_catch_displays() -> void:
    """清空所有钓获展示"""
    for grid in [junk_grid, freshwater_grid, saltwater_grid]:
        if is_instance_valid(grid):
            for child in grid.get_children():
                grid.remove_child(child)
                child.queue_free() 