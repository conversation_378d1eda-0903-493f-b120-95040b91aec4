# SceneSwitcher.gd
# 垂直场景切换管理器 - 负责管理地上和地下场景的切换
# 📁 位置: scripts/core/SceneSwitcher.gd
extends Node

class_name SceneSwitcher

## 信号定义 ##
signal scene_switch_started(from_scene: SceneType, to_scene: SceneType)
signal scene_switch_completed(current_scene: SceneType)

## 枚举定义 ##
enum SceneType {
	OVERGROUND,  # 地上
	UNDERGROUND  # 地下
}

## 常量定义 ##
const SCENE_HEIGHT = 256  # 场景高度
const TRANSITION_DURATION = 1.0  # 过渡动画时间（秒）
const TRANSITION_EASE = Tween.EASE_IN_OUT  # 过渡缓动类型
const TRANSITION_TRANS = Tween.TRANS_CUBIC  # 过渡变换类型

## 属性定义 ##
var camera: Camera2D = null
var current_scene: SceneType = SceneType.OVERGROUND
var is_transitioning: bool = false
var tween: Tween = null

## 场景位置定义 ##
var overground_position: Vector2 = Vector2(960, 128)  # 地上场景相机位置
var underground_position: Vector2 = Vector2(960, 128 + SCENE_HEIGHT)  # 地下场景相机位置

## 初始化 ##
func _ready() -> void:
	# 在Godot 4.x中，直接创建Tween实例
	tween = create_tween()

## 设置相机引用 ##
func set_camera(camera_ref: Camera2D) -> void:
	camera = camera_ref
	if camera:
		# 确保相机初始位置在地上场景
		camera.position = overground_position
		print("SceneSwitcher: 相机已设置，初始位置: ", camera.position)

## 切换到指定场景 ##
func switch_to_scene(target_scene: SceneType) -> void:
	if is_transitioning:
		print("SceneSwitcher: 正在切换中，忽略请求")
		return
	
	if current_scene == target_scene:
		print("SceneSwitcher: 已经在目标场景中")
		return
	
	if not camera:
		push_error("SceneSwitcher: 未设置相机引用！")
		return
	
	_start_transition(target_scene)

## 切换到地上场景 ##
func switch_to_overground() -> void:
	switch_to_scene(SceneType.OVERGROUND)

## 切换到地下场景 ##
func switch_to_underground() -> void:
	switch_to_scene(SceneType.UNDERGROUND)

## 切换场景（地上<->地下） ##
func toggle_scene() -> void:
	if current_scene == SceneType.OVERGROUND:
		switch_to_underground()
	else:
		switch_to_overground()

## 开始过渡动画 ##
func _start_transition(target_scene: SceneType) -> void:
	is_transitioning = true
	var from_scene = current_scene
	current_scene = target_scene
	
	# 发送开始信号
	scene_switch_started.emit(from_scene, target_scene)
	
	# 确定目标位置
	var target_position: Vector2
	match target_scene:
		SceneType.OVERGROUND:
			target_position = overground_position
		SceneType.UNDERGROUND:
			target_position = underground_position
	
	print("SceneSwitcher: 开始切换从 %s 到 %s" % [_scene_type_to_string(from_scene), _scene_type_to_string(target_scene)])
	print("SceneSwitcher: 相机移动从 %s 到 %s" % [camera.position, target_position])
	
	# 停止之前的动画并创建新的Tween
	if tween:
		tween.kill()
	tween = create_tween()
	
	# 设置缓动类型
	tween.set_ease(TRANSITION_EASE)
	tween.set_trans(TRANSITION_TRANS)
	
	# 开始相机移动动画
	tween.tween_property(camera, "position", target_position, TRANSITION_DURATION)
	tween.tween_callback(_on_transition_completed)

## 过渡完成回调 ##
func _on_transition_completed() -> void:
	is_transitioning = false
	print("SceneSwitcher: 场景切换完成，当前场景: %s" % _scene_type_to_string(current_scene))
	
	# 发送完成信号
	scene_switch_completed.emit(current_scene)

## 获取当前场景类型 ##
func get_current_scene() -> SceneType:
	return current_scene

## 检查是否正在过渡 ##
func is_scene_transitioning() -> bool:
	return is_transitioning

## 获取场景类型字符串表示 ##
func _scene_type_to_string(scene_type: SceneType) -> String:
	match scene_type:
		SceneType.OVERGROUND:
			return "地上"
		SceneType.UNDERGROUND:
			return "地下"
		_:
			return "未知"

## 调试信息 ##
func get_debug_info() -> Dictionary:
	return {
		"current_scene": _scene_type_to_string(current_scene),
		"is_transitioning": is_transitioning,
		"camera_position": camera.position if camera else Vector2.ZERO,
		"overground_position": overground_position,
		"underground_position": underground_position
	}

## 高级功能 ##
func set_scene_positions(overground_pos: Vector2, underground_pos: Vector2) -> void:
	"""设置自定义的场景位置"""
	overground_position = overground_pos
	underground_position = underground_pos
	print("SceneSwitcher: 场景位置已更新 - 地上: %s, 地下: %s" % [overground_pos, underground_pos])

func get_scene_position(scene_type: SceneType) -> Vector2:
	"""获取指定场景的位置"""
	match scene_type:
		SceneType.OVERGROUND:
			return overground_position
		SceneType.UNDERGROUND:
			return underground_position
		_:
			return Vector2.ZERO

func set_transition_settings(duration: float, ease_type: Tween.EaseType, trans_type: Tween.TransitionType) -> void:
	"""设置过渡动画参数"""
	# 注意：这些设置将在下次切换时生效
	print("SceneSwitcher: 过渡设置已更新 - 时长: %s, 缓动: %s, 变换: %s" % [duration, ease_type, trans_type])

func force_scene_position(scene_type: SceneType) -> void:
	"""强制切换到指定场景（无动画）"""
	if not camera:
		push_error("SceneSwitcher: 未设置相机引用！")
		return
	
	var old_scene = current_scene
	current_scene = scene_type
	
	match scene_type:
		SceneType.OVERGROUND:
			camera.position = overground_position
		SceneType.UNDERGROUND:
			camera.position = underground_position
	
	print("SceneSwitcher: 强制切换到 %s" % _scene_type_to_string(scene_type))
	scene_switch_completed.emit(current_scene)

## 状态查询 ##
func is_overground() -> bool:
	"""检查是否在地上场景"""
	return current_scene == SceneType.OVERGROUND

func is_underground() -> bool:
	"""检查是否在地下场景"""
	return current_scene == SceneType.UNDERGROUND

func get_transition_progress() -> float:
	"""获取过渡进度（0.0-1.0）"""
	if not is_transitioning or not tween:
		return 1.0 if not is_transitioning else 0.0
	
	# 这里可以添加更精确的进度计算
	return 0.5  # 简化实现

## 事件处理 ##
func pause_transition() -> void:
	"""暂停过渡动画"""
	if tween and is_transitioning:
		tween.pause()
		print("SceneSwitcher: 过渡动画已暂停")

func resume_transition() -> void:
	"""恢复过渡动画"""
	if tween and is_transitioning:
		tween.play()
		print("SceneSwitcher: 过渡动画已恢复")

func cancel_transition() -> void:
	"""取消当前过渡"""
	if tween and is_transitioning:
		tween.kill()
		is_transitioning = false
		print("SceneSwitcher: 过渡动画已取消")
