[gd_scene load_steps=16 format=3 uid="uid://dfg5v1ydohkwe"]

[ext_resource type="Script" path="res://scripts/core/World.gd" id="1_jbsp3"]
[ext_resource type="PackedScene" uid="uid://jgwgs1cyjonm" path="res://scenes/UI/MainUI.tscn" id="2_13sy0"]
[ext_resource type="TileSet" uid="uid://bmblwldpd8sp5" path="res://assets/tilesets/Tileset.tres" id="2_tnph7"]
[ext_resource type="Texture2D" uid="uid://cbumx86nyrt2o" path="res://assets/Hana Caraka/Hana Caraka - Topdown Tileset/Props/Bridge.png" id="3_64s4r"]
[ext_resource type="TileSet" uid="uid://xihdbin6phuc" path="res://assets/tilesets/Tileset_Mining.tres" id="4_n0ola"]
[ext_resource type="Script" path="res://scripts/core/SceneSwitcher.gd" id="6_sceneswitcher"]
[ext_resource type="PackedScene" uid="uid://ct3h54qw2tfef" path="res://scenes/Buildings/Torch_2.tscn" id="7_ybgvr"]
[ext_resource type="PackedScene" uid="uid://csg8pjji8maah" path="res://scenes/Buildings/Tramcar.tscn" id="8_cdpaq"]
[ext_resource type="PackedScene" uid="uid://djw5ac2r40ftg" path="res://scenes/Buildings/Blacksmith.tscn" id="10_7ki18"]
[ext_resource type="Script" path="res://scripts/mining/debug/OreSpawnVisualizer.gd" id="10_visualizer"]
[ext_resource type="PackedScene" uid="uid://dspkwx8kiox3l" path="res://scenes/Buildings/Tradingpost.tscn" id="11_82b28"]
[ext_resource type="PackedScene" uid="uid://cy6ops81yenpe" path="res://scenes/Buildings/AnimalPen.tscn" id="12_wewd6"]

[sub_resource type="NavigationPolygon" id="NavigationPolygon_nhf7w"]
vertices = PackedVector2Array(974, 206, 1010, 198, 1102, 198, 1102, 218, 1010, 218, 974, 114, 1010, 146, 642, 86, 674, 86, 674, 118, 690, 118, 690, 134, 850, 206, 782, 134, 782, 110, 814, 110, 850, 114, 814, 78, 1042, 78, 1042, 146, 1102, 174, 1134, 174, 1134, 110, 1198, 110, 1198, 78, 1362, 78, 1362, 110, 1402, 110, 1402, 250, 1102, 250, 1010, 250, 814, 250, 814, 210, 782, 210, 782, 154, 690, 154, 690, 210, 658, 210, 270, 146, 658, 250, 270, 250, 254, 146, 254, 98, 642, 78, 206, 98, 206, 78, 278, 470, 330, 482, 6, 482, 74, 470, 330, 426, 278, 362, 6, 286, 74, 330, 330, 286, 182, 330, 182, 362, 330, 342, 394, 342, 394, 374, 406, 426, 534, 374, 618, 458, 534, 342, 630, 342, 650, 374, 630, 286, 650, 286, 682, 374, 682, 406, 810, 406, 810, 430, 886, 362, 854, 362, 854, 330, 1002, 286, 822, 330, 822, 286, 1002, 490, 886, 430, 886, 490, 886, 434, 810, 434, 810, 458, 618, 490, 438, 490, 438, 458, 406, 458)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3, 4), PackedInt32Array(1, 0, 5, 6), PackedInt32Array(7, 8, 9), PackedInt32Array(9, 10, 11), PackedInt32Array(12, 13, 14, 15, 16), PackedInt32Array(15, 17, 18, 5, 16), PackedInt32Array(18, 19, 6, 5), PackedInt32Array(2, 20, 21), PackedInt32Array(21, 22, 23), PackedInt32Array(23, 24, 25, 26), PackedInt32Array(26, 27, 28, 29), PackedInt32Array(21, 23, 26, 29, 3), PackedInt32Array(2, 21, 3), PackedInt32Array(12, 0, 4, 30, 31, 32), PackedInt32Array(32, 33, 34, 12), PackedInt32Array(35, 36, 37, 38, 7, 9), PackedInt32Array(37, 39, 40, 38), PackedInt32Array(38, 41, 42, 43, 7), PackedInt32Array(42, 44, 45, 43), PackedInt32Array(35, 9, 11), PackedInt32Array(35, 11, 13, 34), PackedInt32Array(34, 13, 12), PackedInt32Array(46, 47, 48, 49), PackedInt32Array(50, 47, 46, 51), PackedInt32Array(49, 48, 52, 53), PackedInt32Array(53, 52, 54, 55), PackedInt32Array(56, 55, 54, 57, 51), PackedInt32Array(50, 51, 57, 58, 59), PackedInt32Array(60, 50, 59, 61), PackedInt32Array(62, 61, 63, 64, 65), PackedInt32Array(64, 66, 67, 65), PackedInt32Array(65, 68, 69, 62), PackedInt32Array(69, 70, 71), PackedInt32Array(72, 73, 74, 75), PackedInt32Array(74, 76, 77, 75), PackedInt32Array(72, 75, 78, 79), PackedInt32Array(78, 80, 81), PackedInt32Array(79, 78, 81), PackedInt32Array(71, 79, 81, 82), PackedInt32Array(82, 83, 62, 69), PackedInt32Array(62, 84, 85, 86, 61), PackedInt32Array(86, 87, 60, 61), PackedInt32Array(82, 69, 71)])
outlines = Array[PackedVector2Array]([PackedVector2Array(200, 72, 200, 104, 248, 104, 248, 152, 264, 152, 264, 256, 496, 256, 496, 368, 400, 368, 400, 336, 336, 336, 336, 280, 0, 280, 0, 336, 176, 336, 176, 368, 272, 368, 272, 464, 80, 464, 80, 336, 0, 336, 0, 488, 336, 488, 336, 432, 400, 432, 400, 464, 432, 464, 432, 496, 496, 496, 624, 496, 624, 464, 816, 464, 816, 440, 880, 440, 880, 496, 1008, 496, 1008, 280, 816, 280, 816, 336, 848, 336, 848, 368, 880, 368, 880, 424, 816, 424, 816, 400, 688, 400, 688, 368, 656, 368, 656, 280, 624, 280, 624, 336, 528, 336, 528, 368, 496, 368, 496, 368, 496, 256, 664, 256, 664, 216, 696, 216, 696, 160, 776, 160, 776, 216, 808, 216, 808, 256, 1016, 256, 1016, 224, 1096, 224, 1096, 256, 1408, 256, 1408, 104, 1368, 104, 1368, 72, 1192, 72, 1192, 104, 1128, 104, 1128, 168, 1096, 168, 1096, 192, 1016, 192, 1016, 152, 1048, 152, 1048, 72, 928, 72, 928, 120, 968, 120, 968, 200, 856, 200, 856, 120, 928, 120, 928, 72, 808, 72, 808, 104, 776, 104, 776, 128, 696, 128, 696, 112, 680, 112, 680, 80, 648, 80, 648, 72, 488, 72, 488, 72)])
agent_radius = 6.0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_0k8bw"]
texture = ExtResource("3_64s4r")
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
3:0/0 = 0
4:0/0 = 0
5:0/0 = 0
6:0/0 = 0
7:0/0 = 0
8:0/0 = 0
13:0/0 = 0
14:0/0 = 0
15:0/0 = 0
16:0/0 = 0
20:0/0 = 0
21:0/0 = 0
22:0/0 = 0
23:0/0 = 0
0:1/0 = 0
1:1/0 = 0
2:1/0 = 0
3:1/0 = 0
4:1/0 = 0
5:1/0 = 0
6:1/0 = 0
7:1/0 = 0
8:1/0 = 0
9:1/0 = 0
10:1/0 = 0
11:1/0 = 0
13:1/0 = 0
14:1/0 = 0
15:1/0 = 0
16:1/0 = 0
17:1/0 = 0
18:1/0 = 0
19:1/0 = 0
20:1/0 = 0
21:1/0 = 0
22:1/0 = 0
23:1/0 = 0
0:2/0 = 0
1:2/0 = 0
2:2/0 = 0
3:2/0 = 0
4:2/0 = 0
5:2/0 = 0
6:2/0 = 0
7:2/0 = 0
8:2/0 = 0
9:2/0 = 0
10:2/0 = 0
11:2/0 = 0
13:2/0 = 0
14:2/0 = 0
15:2/0 = 0
16:2/0 = 0
17:2/0 = 0
18:2/0 = 0
19:2/0 = 0
20:2/0 = 0
21:2/0 = 0
22:2/0 = 0
23:2/0 = 0
1:3/0 = 0
2:3/0 = 0
3:3/0 = 0
4:3/0 = 0
6:3/0 = 0
7:3/0 = 0
8:3/0 = 0
9:3/0 = 0
10:3/0 = 0
11:3/0 = 0
13:3/0 = 0
14:3/0 = 0
15:3/0 = 0
16:3/0 = 0
20:3/0 = 0
21:3/0 = 0
22:3/0 = 0
23:3/0 = 0
1:4/0 = 0
2:4/0 = 0
3:4/0 = 0
4:4/0 = 0
6:4/0 = 0
7:4/0 = 0
8:4/0 = 0
9:4/0 = 0
10:4/0 = 0
11:4/0 = 0
13:4/0 = 0
14:4/0 = 0
15:4/0 = 0
16:4/0 = 0
20:4/0 = 0
21:4/0 = 0
22:4/0 = 0
23:4/0 = 0
1:5/0 = 0
2:5/0 = 0
3:5/0 = 0
4:5/0 = 0
6:5/0 = 0
7:5/0 = 0
8:5/0 = 0
13:5/0 = 0
14:5/0 = 0
15:5/0 = 0
16:5/0 = 0
20:5/0 = 0
21:5/0 = 0
22:5/0 = 0
23:5/0 = 0
1:6/0 = 0
2:6/0 = 0
3:6/0 = 0
4:6/0 = 0
8:6/0 = 0
9:6/0 = 0
10:6/0 = 0
11:6/0 = 0
13:6/0 = 0
14:6/0 = 0
15:6/0 = 0
16:6/0 = 0
20:6/0 = 0
21:6/0 = 0
22:6/0 = 0
23:6/0 = 0
1:7/0 = 0
2:7/0 = 0
3:7/0 = 0
4:7/0 = 0
5:7/0 = 0
6:7/0 = 0
7:7/0 = 0
8:7/0 = 0
9:7/0 = 0
10:7/0 = 0
11:7/0 = 0
13:7/0 = 0
14:7/0 = 0
15:7/0 = 0
16:7/0 = 0
17:7/0 = 0
18:7/0 = 0
19:7/0 = 0
20:7/0 = 0
21:7/0 = 0
22:7/0 = 0
23:7/0 = 0
1:8/0 = 0
2:8/0 = 0
3:8/0 = 0
4:8/0 = 0
5:8/0 = 0
6:8/0 = 0
7:8/0 = 0
8:8/0 = 0
9:8/0 = 0
10:8/0 = 0
11:8/0 = 0
13:8/0 = 0
14:8/0 = 0
15:8/0 = 0
16:8/0 = 0
17:8/0 = 0
18:8/0 = 0
19:8/0 = 0
20:8/0 = 0
21:8/0 = 0
22:8/0 = 0
23:8/0 = 0
1:9/0 = 0
2:9/0 = 0
3:9/0 = 0
4:9/0 = 0
8:9/0 = 0
9:9/0 = 0
10:9/0 = 0
11:9/0 = 0
13:9/0 = 0
14:9/0 = 0
15:9/0 = 0
16:9/0 = 0
20:9/0 = 0
21:9/0 = 0
22:9/0 = 0
23:9/0 = 0
1:10/0 = 0
2:10/0 = 0
3:10/0 = 0
4:10/0 = 0
8:10/0 = 0
9:10/0 = 0
10:10/0 = 0
11:10/0 = 0
13:10/0 = 0
14:10/0 = 0
15:10/0 = 0
16:10/0 = 0
20:10/0 = 0
21:10/0 = 0
22:10/0 = 0
23:10/0 = 0
1:11/0 = 0
2:11/0 = 0
3:11/0 = 0
4:11/0 = 0
8:11/0 = 0
9:11/0 = 0
10:11/0 = 0
11:11/0 = 0
13:11/0 = 0
14:11/0 = 0
15:11/0 = 0
16:11/0 = 0
20:11/0 = 0
21:11/0 = 0
22:11/0 = 0
23:11/0 = 0

[sub_resource type="TileSet" id="TileSet_a4i7m"]
sources/15 = SubResource("TileSetAtlasSource_0k8bw")

[node name="World" type="Node2D"]
script = ExtResource("1_jbsp3")

[node name="NavigationRegion2D" type="NavigationRegion2D" parent="." groups=["navigation"]]
visible = false
navigation_polygon = SubResource("NavigationPolygon_nhf7w")

[node name="TerrainTileMap" type="TileMap" parent="."]
z_index = -20
scale = Vector2(2, 2)
tile_set = ExtResource("2_tnph7")
format = 2
layer_0/name = "Dirt"
layer_0/tile_data = PackedInt32Array(196619, 65537, 1, 196620, 65537, 1, 196621, 65537, 1, 196622, 65537, 1, 131086, 65537, 0, 131085, 65537, 0, 131084, 65537, 0, 131083, 65537, 0, 131082, 65537, 0, 262155, 65537, 1, 196623, 65537, 1, 262157, 65537, 1, 262156, 65537, 1, 327692, 65537, 1, 327693, 65537, 1, 262158, 65537, 1, 327694, 65537, 1, 327695, 65537, 1, 327696, 65537, 1, 327697, 65537, 1, 327698, 65537, 1, 327699, 65537, 1, 327700, 65537, 1, 262164, 65537, 1, 196628, 65537, 1, 196627, 65537, 1, 196626, 65537, 1, 196625, 65537, 1, 262160, 65537, 1, 262159, 65537, 1, 262161, 65537, 1, 262162, 65537, 1, 262163, 65537, 1, 131090, 65537, 0, 131089, 65537, 0, 131088, 65537, 0, 131087, 65537, 0, 131091, 65537, 0, 131092, 65537, 0, 131093, 131073, 0, 196629, 131073, 1, 262165, 131073, 1, 327701, 131073, 1, 196618, 65537, 1, 196632, 1, 0, 262168, 1, 1, 327704, 1, 1, 327705, 65537, 1, 262169, 65537, 1, 196633, 393217, 2, 131097, 1, 0, 131098, 65537, 0, 327706, 131073, 1, 262170, 131073, 1, 196634, 327681, 1, 131099, 65537, 0, 131100, 65537, 0, 196636, 65537, 2, 196624, 65537, 1, 393229, 65537, 1, 393230, 65537, 1, 393231, 65537, 1, 393232, 65537, 1, 393233, 65537, 1, 393234, 65537, 1, 393235, 65537, 1, 393236, 327681, 1, 393237, 131073, 2, 393240, 1, 2, 393241, 393217, 1, 393242, 327681, 2, 393243, 65537, 0, 393244, 65537, 0, 393228, 65537, 1, 458764, 65537, 1, 458765, 65537, 1, 458766, 65537, 1, 458767, 65537, 1, 458768, 65537, 1, 458769, 65537, 1, 458770, 65537, 1, 458771, 65537, 1, 458772, 131073, 1, 458777, 1, 1, 458778, 65537, 1, 458779, 65537, 1, 458780, 65537, 1, 131101, 65537, 0, 327714, 1, 0, 393250, 1, 1, 458786, 1, 1, 196643, 1, 0, 327715, 393217, 2, 393251, 65537, 1, 458787, 65537, 1, 262180, 65537, 1, 327716, 65537, 1, 393252, 65537, 1, 458788, 65537, 1, 131109, 1, 0, 196645, 393217, 2, 262181, 65537, 1, 327717, 65537, 1, 393253, 65537, 1, 458789, 65537, 1, 131110, 65537, 0, 196646, 65537, 1, 262182, 65537, 1, 327718, 65537, 1, 393254, 65537, 1, 458790, 65537, 1, 131111, 65537, 0, 196647, 65537, 1, 262183, 65537, 1, 327719, 65537, 1, 393255, 65537, 1, 458791, 65537, 1, 196648, 65537, 1, 262184, 65537, 1, 327720, 65537, 1, 393256, 65537, 1, 458792, 65537, 1, 196649, 65537, 1, 262185, 65537, 1, 327721, 65537, 1, 393257, 65537, 1, 458793, 65537, 1, 131112, 65537, 0, 131113, 65537, 0, 131114, 131073, 0, 196650, 327681, 2, 262186, 65537, 1, 327722, 65537, 1, 393258, 65537, 1, 458794, 65537, 1, 196651, 65537, 0, 262187, 65537, 1, 327723, 65537, 1, 393259, 65537, 1, 458795, 65537, 1, 196652, 131073, 0, 262188, 131073, 1, 327724, 131073, 1, 393260, 131073, 1, 458796, 131073, 1, 196637, 65537, 2, 393245, 65537, 0, 458781, 65537, 1, 458782, 65537, 1, 458783, 131073, 1, 393247, 131073, 1, 327711, 131073, 1, 327710, 1, 1, 393246, 393217, 2, 262174, 1, 1, 196638, 393217, 1, 131102, 65537, 0, 131104, 131073, 0, 131103, 65537, 0, 196639, 65537, 1, 262175, 327681, 1, 262176, 131073, 2, 196640, 131073, 1, 196635, 65537, 2, 262179, 1, 1, 196644, 65537, 0, 393227, 65537, 1, 458763, 65537, 1, 458762, 65537, 1, 458761, 65537, 1, 393225, 65537, 1, 327689, 65537, 1, 327690, 65537, 1, 327691, 65537, 1, 262153, 65537, 1, 262154, 65537, 1, 393226, 65537, 1, 458760, 1, 1, 393224, 1, 1, 327688, 1, 1, 262152, 393217, 1, 196617, 65537, 1, 196616, 65537, 1, 196615, 393217, 1, 131080, 65537, 0, 131081, 65537, 0, 262151, 1, 2, 196614, 1, 2, 131078, 1, 0, 131079, 65537, 0)
layer_1/name = "Ground"
layer_1/tile_data = PackedInt32Array(131086, 10, 0, 131087, 65546, 0, 131088, 131082, 0, 196627, 65546, 0, 262162, 65546, 1, 262161, 65546, 1, 262160, 65546, 1, 262159, 65546, 1, 262158, 10, 1, 196622, 10, 1, 196623, 65546, 1, 196624, 327690, 2, 196625, 65546, 0, 196626, 65546, 0, 196628, 327690, 0, 262164, 131082, 1, 327700, 327690, 4, 327699, 65546, 1, 327698, 65546, 1, 327697, 65546, 1, 327696, 65546, 1, 327695, 65546, 1, 327694, 393226, 2, 327693, 65546, 0, 327692, 65546, 0, 393229, 65546, 2, 393230, 393226, 1, 393231, 65546, 1, 393232, 65546, 1, 393233, 65546, 1, 393234, 65546, 1, 393235, 327690, 1, 458771, 131082, 2, 458770, 65546, 2, 458769, 65546, 2, 458768, 65546, 2, 458767, 65546, 2, 458766, 10, 2, 393228, 65546, 2, 393227, 65546, 2, 393226, 65546, 2, 393225, 10, 2, 327689, 10, 0, 327690, 65546, 0, 327691, 65546, 0, 196633, 393226, 4, 262169, 10, 1, 327705, 393226, 2, 327704, 10, 0, 393241, 65546, 2, 393242, 327690, 3, 327706, 131082, 1, 262170, 131082, 1, 196634, 327690, 1, 131098, 65546, 0, 131097, 10, 0, 131099, 65546, 0, 131100, 65546, 0, 131101, 131082, 0, 196636, 65546, 2, 196635, 65546, 2, 196637, 327690, 3, 196638, 524298, 0, 196639, 131082, 3, 262174, 196618, 1, 327710, 196618, 1, 393246, 458762, 2, 393245, 65546, 0, 393244, 65546, 0, 393243, 393226, 0, 458781, 65546, 2, 458782, 131082, 2, 196632, 10, 3, 393240, 10, 2, 196629, 131082, 3, 327701, 131082, 3, 262163, 65546, 1, 393236, 131082, 2, 458780, 65546, 2, 458779, 10, 2, 196616, 131082, 1, 196615, 393226, 1, 196614, 10, 2, 131078, 10, 0, 131079, 65546, 0, 131080, 131082, 0, 262152, 131082, 2, 262151, 10, 2)
layer_2/name = "Water"
layer_2/tile_data = PackedInt32Array(131084, 524328, 0, 131085, 524328, 0, 327693, 524328, 1, 131086, 524328, 0, 196622, 524328, 1, 262158, 524328, 1, 327694, 524328, 1, 131087, 524328, 0, 196623, 524328, 1, 262159, 524328, 1, 327695, 524328, 1, 131088, 524328, 0, 196624, 524328, 1, 262160, 524328, 1, 327696, 524328, 1, 131089, 524328, 0, 196625, 524328, 1, 262161, 524328, 1, 327697, 524328, 1, 131090, 524328, 0, 196626, 524328, 1, 262162, 524328, 1, 327698, 524328, 1, 131091, 524328, 0, 196627, 524328, 1, 262163, 524328, 1, 327699, 524328, 1, 131092, 524328, 0, 196628, 524328, 1, 262164, 524328, 1, 327700, 524328, 1, 131093, 1048616, 0, 196629, 1048616, 1, 262165, 1048616, 1, 327701, 1048616, 1, 131094, 65569, 0, 196630, 65569, 0, 327702, 65569, 0, 131095, 65569, 0, 327703, 65569, 0, 131096, 65569, 0, 196632, 40, 0, 262168, 40, 1, 327704, 40, 1, 131097, 40, 0, 196633, 1048616, 6, 262169, 524328, 1, 327705, 524328, 1, 131098, 524328, 0, 262170, 1048617, 1, 327706, 1048617, 1, 131099, 524328, 0, 196635, 524329, 2, 131100, 524328, 0, 196636, 524329, 2, 393239, 65569, 0, 393240, 40, 2, 393241, 1048616, 5, 393242, 524329, 6, 393243, 524329, 0, 393244, 524329, 0, 393246, 1048617, 6, 196637, 524329, 2, 327710, 41, 1, 262174, 41, 1, 131102, 524328, 0, 393229, 524328, 1, 393230, 524328, 1, 393231, 524328, 1, 393232, 524328, 1, 393233, 524328, 1, 393234, 524328, 1, 393235, 524328, 1, 393236, 524328, 5, 393237, 1048616, 2, 458773, 65569, 0, 458774, 65569, 0, 458775, 65569, 0, 458776, 65569, 0, 458777, 40, 1, 458778, 524328, 1, 458779, 524328, 1, 458780, 524328, 1, 458782, 524328, 1, 393238, 65569, 0, 131101, 524328, 0, 458781, 524328, 1, 393245, 524329, 0, 131106, 65569, 0, 196642, 65569, 0, 262178, 65569, 0, 327714, 40, 0, 393250, 40, 1, 458786, 40, 1, 131107, 65569, 0, 196643, 40, 0, 262179, 40, 1, 327715, 1048616, 6, 393251, 524328, 1, 458787, 524328, 1, 131108, 65569, 0, 196644, 524328, 0, 262180, 524328, 1, 327716, 524328, 1, 393252, 524328, 1, 458788, 524328, 1, 131109, 40, 0, 196645, 1048616, 6, 262181, 524328, 1, 327717, 524328, 1, 393253, 524328, 1, 458789, 524328, 1, 131110, 524328, 0, 196646, 524328, 1, 262182, 524328, 1, 327718, 524328, 1, 393254, 524328, 1, 458790, 524328, 1, 131111, 524328, 0, 196647, 524328, 1, 262183, 524328, 1, 327719, 524328, 1, 393255, 524328, 1, 458791, 524328, 1, 131112, 524328, 0, 196648, 524328, 1, 262184, 524328, 1, 327720, 524328, 1, 393256, 524328, 1, 458792, 524328, 1, 131113, 524328, 0, 196649, 524328, 1, 262185, 524328, 1, 327721, 524328, 1, 393257, 524328, 1, 458793, 524328, 1, 131114, 1048616, 0, 196650, 524328, 6, 262186, 524328, 1, 327722, 524328, 1, 393258, 524328, 1, 458794, 524328, 1, 131115, 65569, 0, 196651, 524328, 0, 262187, 524328, 1, 327723, 524328, 1, 393259, 524328, 1, 458795, 524328, 1, 131116, 65569, 0, 196652, 1048616, 0, 262188, 1048616, 1, 327724, 1048616, 1, 393260, 1048616, 1, 458796, 1048616, 1, 458783, 1048616, 1, 393247, 1048616, 1, 327711, 1048616, 1, 131103, 524328, 0, 131104, 1048616, 0, 131105, 65569, 0, 196641, 65569, 0, 196640, 1048616, 1, 262176, 1048616, 2, 262177, 65569, 0, 327713, 65569, 0, 458784, 65569, 0, 458785, 65569, 0, 262175, 524328, 5, 196639, 524328, 1, 327712, 65569, 0, 262171, 33, 1, 262172, 33, 1, 262173, 33, 1, 327709, 33, 1, 327708, 33, 1, 327707, 33, 1, 196638, 1048617, 5, 196634, 524329, 5, 196631, 65569, 0, 131083, 524328, 0, 458767, 524328, 1, 458766, 524328, 1, 458768, 524328, 1, 458769, 524328, 1, 458770, 524328, 1, 458771, 524328, 1, 458772, 1048616, 1, 262157, 524328, 1, 196621, 524328, 1, 196620, 524328, 1, 131082, 524328, 0, 393223, 65569, 0, 458759, 65569, 0, 458758, 65569, 0, 458757, 65569, 0, 458756, 65569, 0, 458755, 65569, 0, 458754, 65569, 0, 393219, 65569, 0, 393220, 65569, 0, 327685, 65569, 0, 327686, 65569, 0, 262151, 40, 2, 393222, 65569, 0, 393221, 65569, 0, 196617, 524328, 1, 131081, 524328, 0, 131080, 524328, 0, 131079, 524328, 0, 131078, 40, 0, 131077, 65569, 0, 131076, 65569, 0, 131075, 65569, 0, 131074, 65569, 0, 196611, 65569, 0, 196612, 65569, 0, 196613, 65569, 0, 196614, 40, 2, 196615, 1048616, 5, 196616, 524328, 1, 262150, 65569, 0, 262149, 65569, 0, 262148, 65569, 0, 262147, 65569, 0, 262146, 65569, 0, 262145, 65569, 0, 262144, 65569, 0, 196608, 65569, 0, 131072, 65569, 0, 131073, 65569, 0, 196610, 65569, 0, 196609, 65569, 0, 327684, 65569, 0, 327683, 65569, 0, 327682, 65569, 0, 327681, 65569, 0, 327680, 65569, 0, 393216, 65569, 0, 393217, 65569, 0, 393218, 65569, 0, 458753, 65569, 0, 458752, 65569, 0, 196619, 524328, 1, 196618, 524328, 1, 262156, 524328, 1, 262155, 524328, 1, 262154, 524328, 1, 262153, 524328, 1, 327692, 524328, 1, 393228, 524328, 1, 393227, 524328, 1, 327687, 65569, 0, 262152, 1048616, 5, 327688, 40, 1, 327691, 524328, 1, 327690, 524328, 1, 393226, 524328, 1, 393224, 40, 1, 327689, 524328, 1, 393225, 524328, 1, 458760, 40, 1)
layer_3/name = "Features"
layer_3/tile_data = PackedInt32Array(131094, 262194, 4, 196631, 196658, 1, 131096, 393266, 5, 262169, 48, 2, 131093, 131117, 0, 196629, 196653, 2, 131092, 45, 3, 458786, 45, 3, 262171, 262194, 2, 327709, 262194, 1, 262173, 65586, 1, 327708, 131122, 4, 131078, 45, 3, 196614, 45, 3, 131079, 65581, 3, 196615, 131117, 0, 262151, 196653, 2, 131080, 65581, 3, 131081, 131117, 3, 458787, 131117, 3)

[node name="ObjectsTileMap" type="TileMap" parent="."]
z_index = -15
y_sort_enabled = true
scale = Vector2(2, 2)
tile_set = SubResource("TileSet_a4i7m")
format = 2
layer_0/name = "Buildings"
layer_0/y_sort_enabled = true
layer_0/tile_data = PackedInt32Array(196630, 196623, 6, 262166, 196623, 7, 327702, 196623, 8, 196631, 262159, 6, 262167, 262159, 7, 327703, 262159, 8, 196632, 327695, 0, 327704, 327695, 2, 262168, 327695, 1, 196629, 15, 0, 262165, 15, 1, 327701, 15, 2, 327711, 15, 0, 393247, 15, 1, 458783, 15, 2, 327712, 196623, 6, 393248, 196623, 7, 458784, 196623, 8, 327713, 262159, 6, 393249, 262159, 7, 458785, 262159, 8, 327714, 327695, 0, 393250, 327695, 1, 458786, 327695, 2)
layer_1/name = "Objects"
layer_1/y_sort_enabled = true

[node name="MiningTileMap" type="TileMap" parent="."]
scale = Vector2(2, 2)
tile_set = ExtResource("4_n0ola")
format = 2
layer_0/name = "Ground"
layer_0/tile_data = PackedInt32Array(917517, 262144, 0, 917518, 65536, 1, 917519, 65536, 1, 917520, 65536, 1, 917521, 65536, 1, 983053, 0, 2, 983054, 262144, 5, 983055, 65536, 2, 983056, 262144, 5, 983057, 65536, 2, 917522, 65536, 1, 917523, 196608, 0, 917524, 65536, 5, 917525, 262144, 5, 851990, 65536, 1, 851991, 65536, 1, 786454, 65536, 0, 786455, 65536, 3, 786456, 262144, 3, 917526, 65536, 5, 917527, 458752, 5, 917528, 65536, 2, 917529, 131072, 2, 983059, 131072, 2, 983058, 262144, 5, 786457, 131072, 0, 983050, 131072, 2, 917516, 0, 2, 524313, 0, 0, 720922, 0, 2, 655385, 0, 2, 655387, 65536, 1, 655388, 65536, 1, 655389, 65536, 1, 589850, 65536, 1, 589851, 65536, 1, 589852, 65536, 1, 589853, 65536, 1, 655390, 65536, 1, 589854, 65536, 1, 589855, 524288, 4, 655391, 131072, 1, 524319, 131072, 0, 524318, 65536, 3, 524317, 458752, 3, 524316, 65536, 3, 524315, 458752, 3, 589849, 393216, 4, 524314, 65536, 3, 655386, 262144, 0, 720923, 262144, 0, 786459, 0, 4, 851995, 0, 4, 917531, 0, 4, 917532, 65536, 1, 917533, 65536, 1, 917534, 65536, 1, 917535, 327680, 4, 851999, 131072, 4, 786463, 131072, 1, 720927, 327680, 4, 720926, 65536, 1, 786462, 65536, 1, 851998, 65536, 1, 851997, 65536, 1, 851996, 65536, 1, 786460, 65536, 1, 720924, 65536, 1, 720925, 65536, 1, 786461, 65536, 1, 983067, 0, 2, 851993, 524288, 4, 851992, 65536, 1, 983070, 458752, 5, 983069, 262144, 5, 983068, 65536, 5, 983071, 131072, 2, 720906, 65536, 1, 786442, 65536, 1, 655371, 65536, 1, 720907, 65536, 1, 786443, 65536, 1, 720908, 65536, 1, 786444, 65536, 1, 720909, 196608, 1, 786445, 65536, 1, 720910, 458752, 3, 786446, 65536, 1, 720911, 262144, 1, 786447, 65536, 1, 720912, 65536, 1, 786448, 65536, 1, 655377, 65536, 1, 720913, 65536, 1, 786449, 65536, 1, 655378, 65536, 1, 720914, 65536, 1, 786450, 65536, 1, 589843, 65536, 1, 655379, 65536, 1, 720915, 65536, 1, 786451, 65536, 1, 589844, 327680, 4, 655380, 524288, 4, 720916, 196608, 1, 786452, 65536, 1, 720917, 131072, 0, 786453, 196608, 1, 851979, 458752, 5, 851980, 262144, 0, 851981, 65536, 1, 851982, 65536, 1, 851983, 65536, 1, 851984, 65536, 1, 851985, 65536, 1, 851986, 65536, 1, 851987, 65536, 1, 851988, 65536, 1, 851989, 65536, 1, 524308, 131072, 0, 524307, 262144, 3, 524297, 65536, 0, 524296, 65536, 3, 524295, 458752, 3, 589833, 65536, 1, 589832, 65536, 1, 655370, 65536, 1, 655369, 65536, 1, 655368, 65536, 1, 720905, 65536, 1, 720904, 262144, 0, 786441, 65536, 1, 786440, 0, 4, 851977, 65536, 1, 589831, 65536, 1, 655367, 65536, 1, 917513, 65536, 1, 917512, 262144, 1, 917511, 65536, 3, 851978, 196608, 0, 851976, 196608, 4, 983047, 458752, 5, 983046, 262144, 5, 983045, 262144, 5, 917508, 262144, 3, 917507, 262144, 3, 917509, 65536, 3, 917510, 65536, 3, 983044, 65536, 2, 983043, 65536, 2, 983042, 262144, 5, 917506, 196608, 1, 589830, 65536, 1, 655366, 65536, 1, 655365, 262144, 0, 655364, 262144, 5, 655363, 65536, 5, 589826, 65536, 1, 589829, 65536, 1, 589828, 65536, 1, 589827, 65536, 1, 524290, 458752, 3, 524289, 458752, 3, 524291, 262144, 3, 524292, 65536, 0, 524293, 65536, 3, 524294, 262144, 3, 589825, 65536, 1, 589824, 65536, 1, 589823, 0, 0, 655359, 0, 4, 655362, 196608, 0, 655361, 65536, 1, 655360, 65536, 1, 720895, 0, 4, 720903, 65536, 5, 720902, 458752, 5, 720901, 0, 2, 983041, 65536, 2, 917505, 65536, 1, 851970, 524288, 4, 786434, 524288, 4, 720898, 131072, 1, 786433, 65536, 1, 851969, 65536, 1, 917504, 65536, 1, 983040, 458752, 5, 1048575, 0, 2, 983039, 0, 1, 917503, 0, 4, 851967, 0, 1, 786431, 0, 4, 720896, 65536, 1, 786432, 65536, 1, 851968, 65536, 1, 720897, 65536, 1, 524288, 65536, 0, 917514, 524288, 4, 983049, 65536, 2, 983048, 65536, 2, 655372, 196608, 1, 655376, 65536, 1, 524298, 262144, 3, 589834, 65536, 1, 589842, 262144, 1, 524306, 0, 0, 589835, 196608, 1, 524299, 131072, 0, 589836, 131072, 0, 655373, 131072, 0, 655375, 196608, 4, 589841, 65536, 0, 589840, 458752, 3, 589839, 0, 0)
layer_1/name = "Path"
layer_1/tile_data = PackedInt32Array(720914, 65550, 0, 720913, 14, 0, 786449, 14, 2, 786450, 65550, 2, 786451, 131086, 2, 720915, 131086, 0, 786445, 393231, 4, 786446, 65551, 1, 786447, 327695, 2, 851983, 65551, 2, 851982, 524303, 1, 917518, 196623, 2, 851981, 15, 2, 720909, 15, 0, 720910, 65551, 0, 720911, 131087, 0, 786448, 131087, 0, 851984, 589839, 0, 851985, 65551, 0, 851986, 327695, 0, 851987, 65551, 3, 851988, 131087, 3, 917522, 131087, 2, 917521, 65551, 2, 917520, 15, 2, 786444, 65551, 3, 786443, 65551, 3, 655369, 131087, 2, 655368, 65551, 2, 655367, 65551, 2, 655366, 15, 2, 589830, 15, 0, 589831, 65551, 0, 589832, 65551, 0, 589833, 131087, 0, 786442, 15, 3)
layer_2/name = "Wall"
layer_2/tile_data = PackedInt32Array(589837, 65549, 0, 589840, 262157, 0, 655376, 262157, 1, 589836, 196621, 0, 655373, 65549, 0, 655374, 65549, 0, 589839, 65549, 0, 589838, 65549, 0, 655372, 196621, 1, 589835, 65549, 0, 524299, 65549, 0, 524300, 65549, 0, 589841, 65549, 0, 589842, 65549, 0, 589843, 131085, 0, 524306, 65549, 0, 524305, 65549, 0, 524304, 13, 0, 720909, 65549, 1, 720910, 65549, 1, 655375, 65549, 0, 720911, 65549, 1, 720908, 13, 1, 720912, 131085, 1, 655377, 65549, 1, 655378, 65549, 1, 655379, 131085, 1, 524307, 131085, 0, 655371, 65549, 1, 589834, 13, 0, 655370, 13, 1, 524298, 13, 0)
layer_3/name = "Water"
layer_3/tile_data = PackedInt32Array(589846, 524304, 1, 589845, 524304, 1, 589848, 524304, 1, 589847, 524304, 1, 589849, 1048592, 1, 589844, 16, 1, 1048595, 524304, 2, 1048594, 524304, 2, 1048593, 524304, 2, 1048589, 524304, 2, 1048588, 524304, 2, 983065, 524304, 1, 851993, 16, 1, 786457, 524304, 3, 786456, 524304, 2, 786455, 524304, 2, 786453, 16, 2, 983060, 524304, 1, 983061, 524304, 1, 983062, 524304, 1, 983063, 524304, 1, 983064, 524304, 1, 1048601, 524304, 2, 1048600, 524304, 2, 1048599, 524304, 2, 1048598, 524304, 2, 1048597, 524304, 2, 1048596, 524304, 2, 1048587, 524304, 2, 983059, 524304, 4, 917524, 524304, 0, 917523, 16, 0, 917525, 524304, 0, 917526, 524304, 0, 917527, 524304, 0, 917528, 524304, 0, 917529, 524304, 4, 917530, 524304, 1, 983066, 524304, 1, 983067, 16, 4, 851994, 524304, 1, 786458, 524304, 1, 720921, 524304, 1, 720922, 16, 4, 983057, 524304, 0, 983058, 524304, 0, 720920, 524304, 1, 720919, 524304, 1, 720918, 524304, 1, 655386, 1048592, 0, 655382, 524304, 1, 655383, 524304, 1, 655385, 16, 4, 655384, 524304, 1, 720917, 524304, 3, 655381, 524304, 1, 786454, 524304, 2, 655380, 16, 1, 720916, 16, 2, 786459, 1048592, 1, 851995, 1048592, 1, 720923, 1048592, 0, 917531, 1048592, 1, 1048603, 524304, 2, 1048602, 524304, 2, 1048604, 524304, 2, 1048605, 524304, 2, 1048606, 524304, 2, 1048607, 1048592, 2, 983071, 1048592, 0, 983070, 524304, 0, 983069, 524304, 0, 983068, 524304, 0, 1048586, 524304, 2, 1048585, 524304, 2, 1048582, 524304, 2, 1048581, 524304, 2, 1048580, 524304, 2, 1048579, 524304, 2, 1048578, 524304, 2, 1114111, 16, 2, 1048575, 16, 0, 983040, 524304, 0, 1048577, 524304, 2, 1048576, 524304, 2, 524311, 524305, 2, 524310, 524305, 2, 524309, 524305, 2, 524308, 524305, 2, 524313, 524305, 2, 524312, 524305, 2, 983047, 524304, 0, 1048583, 524304, 2, 983041, 524304, 0, 983048, 524304, 0, 1048584, 524304, 2, 983042, 524304, 0, 983043, 524304, 0, 851978, 16, 0, 917514, 16, 1, 983044, 524304, 0, 851979, 524304, 0, 917515, 524304, 1, 983051, 524304, 1, 983045, 524304, 0, 851980, 1048592, 0, 917516, 16, 4, 983052, 524304, 1, 983046, 524304, 0, 917517, 1048592, 0, 983053, 16, 4, 983054, 524304, 0, 1048590, 524304, 2, 983055, 524304, 0, 1048591, 524304, 2, 983049, 524304, 0, 983056, 524304, 0, 1048592, 524304, 2, 983050, 524304, 4, 655362, 19, 0, 720898, 19, 1, 786434, 19, 1, 851970, 19, 1, 917506, 19, 2, 655363, 524307, 0, 720899, 524307, 1, 786435, 524307, 1, 851971, 524307, 1, 917507, 524307, 2, 655364, 524307, 0, 720900, 524307, 1, 786436, 524307, 1, 851972, 524307, 1, 917508, 524307, 2, 655365, 1048595, 0, 720901, 19, 4, 786437, 524307, 1, 851973, 524307, 1, 917509, 524307, 2, 720902, 524307, 0, 786438, 524307, 1, 851974, 524307, 1, 917510, 524307, 2, 720903, 524307, 0, 786439, 524307, 1, 851975, 524307, 1, 917511, 524307, 2, 720904, 1048595, 0, 786440, 1048595, 1, 851976, 1048595, 1, 917512, 1048595, 2)
layer_4/name = "Wall cave"
layer_4/tile_data = PackedInt32Array(524313, 8, 2, 524308, 131080, 2, 524297, 65544, 5, 524314, 458760, 5, 524315, 262152, 5, 524316, 65544, 5, 524317, 262152, 5, 524318, 65544, 5, 524319, 131080, 2, 524307, 65544, 2, 524306, 458760, 5, 524299, 65544, 2, 524298, 262152, 5, 524305, 262152, 5, 524304, 196616, 0, 524303, 65544, 1, 524296, 262152, 5, 524295, 458760, 5, 524294, 458760, 5, 524293, 65544, 2, 524292, 65544, 2, 524291, 65544, 5, 524290, 262152, 5, 524289, 458760, 5, 524288, 65544, 5, 589823, 8, 2, 589838, 458760, 5, 589840, 131080, 2, 589839, 458760, 5, 524302, 65544, 1, 524301, 65544, 1, 524300, 262152, 0, 589837, 262152, 5, 589836, 8, 2)
layer_5/name = "Railway"
layer_5/tile_data = PackedInt32Array(851982, 29, 2, 851983, 131101, 3, 851987, 65565, 3, 851988, 131101, 3, 851992, 65565, 3, 851985, 29, 3, 917522, 131103, 2, 851986, 393245, 2, 786446, 393245, 0, 851993, 65565, 3, 851994, 65565, 3, 851995, 65565, 3, 786450, 196637, 0, 851991, 29, 3, 851996, 131101, 2, 786460, 196637, 1, 720924, 196637, 0, 589851, 262177, 9, 655387, 262177, 10, 589852, 327713, 9, 655388, 327713, 10, 589853, 393249, 9, 655389, 393249, 10, 786445, 65565, 3, 786444, 65565, 3, 786443, 65565, 3, 720910, 196637, 0, 786442, 29, 3, 655369, 131101, 0, 655368, 65565, 3, 655367, 29, 3, 720905, 196637, 2)
layer_6/name = "Waterfall"
layer_6/tile_data = PackedInt32Array(524309, 262166, 0, 524310, 262166, 0, 524312, 262166, 0, 524311, 262166, 0, 524313, 524310, 0, 524308, 22, 0, 589846, 262166, 3, 589845, 262166, 3, 589848, 262166, 3, 589847, 262166, 3, 589849, 524310, 3, 589844, 22, 3)
layer_7/name = "Building"
layer_7/tile_data = PackedInt32Array(655374, 26, 0)

[node name="YSortLayer" type="Node2D" parent="."]
y_sort_enabled = true

[node name="Characters" type="Node2D" parent="YSortLayer"]

[node name="UndergroundLayer" type="Node2D" parent="."]
y_sort_enabled = true
position = Vector2(0, 256)

[node name="SceneSwitcher" type="Node" parent="."]
script = ExtResource("6_sceneswitcher")

[node name="SceneSwitchUI" type="CanvasLayer" parent="."]

[node name="SceneSwitchButton" type="Button" parent="SceneSwitchUI"]
offset_left = 20.0
offset_top = 20.0
offset_right = 120.0
offset_bottom = 60.0
theme_override_font_sizes/font_size = 14
text = "切换到地下"
metadata/_edit_use_anchors_ = true

[node name="UI" type="CanvasLayer" parent="."]

[node name="MainUI" parent="UI" instance=ExtResource("2_13sy0")]
offset_left = 448.0
offset_right = 960.0
metadata/_edit_use_anchors_ = true

[node name="PlayerCamera" type="Camera2D" parent="."]
position = Vector2(960, 128)

[node name="Torch2" parent="." instance=ExtResource("7_ybgvr")]
position = Vector2(424, 362)

[node name="Torch3" parent="." instance=ExtResource("7_ybgvr")]
position = Vector2(504, 362)

[node name="Tramcar" parent="." instance=ExtResource("8_cdpaq")]
position = Vector2(328, 384)

[node name="OreSpawnVisualizer" type="Node2D" parent="."]
script = ExtResource("10_visualizer")

[node name="Blacksmith" parent="." instance=ExtResource("10_7ki18")]
position = Vector2(368, 80)

[node name="Tradingpost" parent="." instance=ExtResource("11_82b28")]
position = Vector2(1264, 70)

[node name="AnimalPen" parent="." instance=ExtResource("12_wewd6")]
position = Vector2(520, 88)
