# 角色系统测试标准

## 📋 测试原则

### 🎯 核心目标
- **简化测试结构** - 避免过多测试文件
- **集中测试场景** - 统一测试入口
- **清晰测试逻辑** - 易于理解和维护
- **快速执行** - 控制在30秒内完成

### ⚠️ 禁止的测试模式
- ❌ 不要创建迁移测试 - 没有迁移，只有替换
- ❌ 不要创建兼容性测试 - 没有兼容性，只有统一性
- ❌ 不要创建多个测试文件 - 集中到一个测试场景
- ❌ 不要测试旧系统 - 旧系统已被删除

## 🏗️ 标准测试架构

### 文件结构
```
scenes/test/
└── StateTestScene.tscn          # 统一测试场景

scripts/test/
├── CharacterSystemTest.gd       # 统一测试脚本
└── TestRunner.gd               # 测试运行器
```

### 测试场景配置
```
StateTestScene.tscn
├── TestRunner (Node)           # 测试运行器
├── TestWorld (Node2D)          # 测试世界环境
│   ├── Farmland (Node2D)       # 测试用农田
│   ├── Well (Node2D)           # 测试用水井
│   └── Storage (Node2D)        # 测试用仓库
└── UI (CanvasLayer)            # 测试UI界面
    └── TestOutput (RichTextLabel)
```

## 🧪 标准测试内容

### 1. 统一状态系统测试
```gdscript
func test_unified_state_system() -> bool:
    """测试统一状态系统的核心功能"""
    var passed = true
    
    # 测试状态枚举完整性
    if not test_state_enum_completeness():
        passed = false
    
    # 测试状态转换逻辑
    if not test_state_transitions():
        passed = false
    
    # 测试状态映射功能
    if not test_state_mapping():
        passed = false
    
    return passed
```

### 2. 角色功能测试
```gdscript
func test_character_functionality(character_type: String) -> bool:
    """测试特定角色的功能完整性"""
    var character = create_test_character(character_type)
    var passed = true
    
    # 测试状态转换
    if not test_character_state_changes(character):
        passed = false
    
    # 测试管理器同步
    if not test_manager_synchronization(character):
        passed = false
    
    # 测试核心功能
    if not test_character_core_functions(character):
        passed = false
    
    character.queue_free()
    return passed
```

### 3. 系统集成测试
```gdscript
func test_system_integration() -> bool:
    """测试系统整体集成"""
    var passed = true
    
    # 测试多角色协作
    if not test_multi_character_interaction():
        passed = false
    
    # 测试性能表现
    if not test_performance_metrics():
        passed = false
    
    # 测试错误处理
    if not test_error_handling():
        passed = false
    
    return passed
```

## 📊 测试执行标准

### 测试运行流程
```gdscript
func run_all_tests() -> void:
    """标准测试执行流程"""
    print("=== 开始角色系统测试 ===")
    var start_time = Time.get_unix_time_from_system()
    
    var results = []
    
    # 1. 基础系统测试
    results.append(run_basic_system_test())
    
    # 2. 角色功能测试
    for character_type in ["farmer", "lumberjack", "fisherman"]:
        results.append(run_character_test(character_type))
    
    # 3. 集成测试
    results.append(run_integration_test())
    
    # 生成测试报告
    generate_test_report(results, start_time)
```

### 测试结果标准
```gdscript
# 测试结果结构
var test_result = {
    "name": "测试名称",
    "passed": true/false,
    "duration": 0.0,
    "details": [],
    "errors": []
}
```

## 🎯 角色测试模板

### 新角色测试实现
```gdscript
func test_new_character_type(character_type: String) -> Dictionary:
    """新角色类型的标准测试模板"""
    var result = {
        "name": character_type + "角色测试",
        "passed": false,
        "details": []
    }
    
    var character = create_test_character(character_type)
    if not character:
        result.details.append("无法创建" + character_type + "角色")
        return result
    
    var all_passed = true
    
    # 1. 状态系统测试
    if not test_character_states(character):
        all_passed = false
        result.details.append("状态系统测试失败")
    
    # 2. 管理器测试
    if not test_character_managers(character):
        all_passed = false
        result.details.append("管理器测试失败")
    
    # 3. 功能测试
    if not test_character_abilities(character):
        all_passed = false
        result.details.append("功能测试失败")
    
    character.queue_free()
    result.passed = all_passed
    
    return result
```

## 🔧 测试工具方法

### 角色创建工具
```gdscript
func create_test_character(character_type: String) -> Node:
    """创建测试用角色"""
    var character_scene_path = "res://scenes/characters/" + character_type + ".tscn"
    var character_scene = load(character_scene_path)
    
    if not character_scene:
        print("错误：无法加载角色场景 " + character_scene_path)
        return null
    
    var character = character_scene.instantiate()
    test_world.add_child(character)
    
    # 设置测试位置
    character.global_position = Vector2(500, 200)
    
    # 等待初始化完成
    await character.ready
    
    return character
```

### 状态验证工具
```gdscript
func verify_state_transition(character: Node, from_state: int, to_state: int) -> bool:
    """验证状态转换"""
    var initial_state = character.get_unified_state()
    if initial_state != from_state:
        print("错误：初始状态不匹配，期望 %d，实际 %d" % [from_state, initial_state])
        return false
    
    var success = character.change_unified_state(to_state)
    if not success:
        print("错误：状态转换失败 %d -> %d" % [from_state, to_state])
        return false
    
    var final_state = character.get_unified_state()
    if final_state != to_state:
        print("错误：最终状态不匹配，期望 %d，实际 %d" % [to_state, final_state])
        return false
    
    return true
```

## 📋 测试检查清单

### 每次测试必须验证
- [ ] 统一状态系统正常工作
- [ ] 所有状态转换成功
- [ ] 管理器状态同步正确
- [ ] 角色核心功能完整
- [ ] 无语法错误和警告
- [ ] 性能指标在预期范围内

### 新角色添加检查清单
- [ ] 扩展UnifiedCharacterStates枚举
- [ ] 实现角色特定状态
- [ ] 添加角色测试用例
- [ ] 验证与现有系统兼容
- [ ] 更新测试文档

## 🚀 测试最佳实践

### 1. 测试编写原则
- **简单明确** - 每个测试只验证一个功能点
- **独立运行** - 测试之间不能相互依赖
- **快速执行** - 单个测试控制在5秒内
- **清晰输出** - 失败时提供明确的错误信息

### 2. 测试维护原则
- **及时更新** - 代码变更时立即更新测试
- **定期清理** - 移除过时的测试用例
- **文档同步** - 保持测试文档与代码同步

### 3. 测试扩展原则
- **复用模板** - 使用标准测试模板
- **统一接口** - 保持测试接口一致
- **渐进增强** - 逐步增加测试覆盖范围

---

**重要提醒**: 测试的目的是验证统一状态系统的正确性，不是验证迁移过程。保持测试简单、集中、高效。
