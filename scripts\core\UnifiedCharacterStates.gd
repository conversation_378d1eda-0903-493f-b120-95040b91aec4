# UnifiedCharacterStates.gd
# 统一的角色状态系统 - 替代多套状态枚举
class_name UnifiedCharacterStates

## 统一状态枚举 ##
enum State {
    # === 基础状态 (0-9) ===
    IDLE = 0,           # 空闲状态
    MOVING = 1,         # 移动状态  
    RESTING = 2,        # 休息状态
    CARRYING = 3,       # 携带物品状态
    
    # === 通用工作状态 (10-19) ===
    COLLECTING = 10,    # 收集物品
    STORING = 11,       # 存储物品
    
    # === 农夫专用状态 (20-29) ===
    HARVESTING = 20,    # 收获作物
    PLANTING = 21,      # 种植作物
    WATERING = 22,      # 浇水
    FETCHING_WATER = 23, # 取水
    
    # === 伐木工专用状态 (30-39) ===
    CHOPPING = 30,      # 砍伐树木
    PROCESSING_WOOD = 31, # 处理木材
    
    # === 渔夫专用状态 (40-49) ===
    FISHING = 40,       # 钓鱼
    PREPARING_BAIT = 41, # 准备鱼饵
    CASTING = 42,       # 抛竿
    REELING = 43,       # 收线
    CATCHING = 44,      # 捕获
    
    # === 厨师专用状态 (50-59) ===
    COOKING = 50,       # 烹饪
    PREPARING_INGREDIENTS = 51, # 准备食材
    
    # === 矿工专用状态 (60-69) ===
    MINING = 60,        # 挖矿
    SMELTING = 61,      # 冶炼
    TRAVELING_UNDERGROUND = 62, # 地下移动
    
    # === 预留扩展状态 (70-99) ===
    # 为未来新角色预留状态空间
}

## 状态分组定义 ##
const STATE_GROUPS = {
    "basic": [State.IDLE, State.MOVING, State.RESTING, State.CARRYING],
    "common_work": [State.COLLECTING, State.STORING],
    "farmer": [State.HARVESTING, State.PLANTING, State.WATERING, State.FETCHING_WATER],
    "woodcutter": [State.CHOPPING, State.PROCESSING_WOOD],
    "fisherman": [State.FISHING, State.PREPARING_BAIT, State.CASTING, State.REELING, State.CATCHING],
    "chef": [State.COOKING, State.PREPARING_INGREDIENTS],
    "miner": [State.MINING, State.SMELTING, State.TRAVELING_UNDERGROUND]
}

## 状态属性定义 ##
const STATE_PROPERTIES = {
    # 基础状态属性
    State.IDLE: {"is_working": false, "animation": "idle", "category": "basic"},
    State.MOVING: {"is_working": false, "animation": "walk", "category": "basic"},
    State.RESTING: {"is_working": false, "animation": "idle", "category": "basic"},
    State.CARRYING: {"is_working": false, "animation": "walk_carry", "category": "basic"},
    
    # 通用工作状态属性
    State.COLLECTING: {"is_working": true, "animation": "collecting", "category": "common_work"},
    State.STORING: {"is_working": true, "animation": "storing", "category": "common_work"},
    
    # 农夫状态属性
    State.HARVESTING: {"is_working": true, "animation": "harvest", "category": "farmer"},
    State.PLANTING: {"is_working": true, "animation": "plant", "category": "farmer"},
    State.WATERING: {"is_working": true, "animation": "water", "category": "farmer"},
    State.FETCHING_WATER: {"is_working": true, "animation": "collecting", "category": "farmer"},
    
    # 伐木工状态属性
    State.CHOPPING: {"is_working": true, "animation": "chop", "category": "woodcutter"},
    State.PROCESSING_WOOD: {"is_working": true, "animation": "process", "category": "woodcutter"},
    
    # 渔夫状态属性
    State.FISHING: {"is_working": true, "animation": "fish", "category": "fisherman"},
    State.PREPARING_BAIT: {"is_working": true, "animation": "prepare", "category": "fisherman"},
    State.CASTING: {"is_working": true, "animation": "cast", "category": "fisherman"},
    State.REELING: {"is_working": true, "animation": "reel", "category": "fisherman"},
    State.CATCHING: {"is_working": true, "animation": "catch", "category": "fisherman"},
    
    # 厨师状态属性
    State.COOKING: {"is_working": true, "animation": "cook", "category": "chef"},
    State.PREPARING_INGREDIENTS: {"is_working": true, "animation": "prepare", "category": "chef"},
    
    # 矿工状态属性
    State.MINING: {"is_working": true, "animation": "mine", "category": "miner"},
    State.SMELTING: {"is_working": true, "animation": "smelt", "category": "miner"},
    State.TRAVELING_UNDERGROUND: {"is_working": false, "animation": "walk", "category": "miner"}
}

## 基类状态映射 ##
const BASE_CHARACTER_STATE_MAPPING = {
    State.IDLE: 0,           # CharacterSpecificState.IDLE
    State.MOVING: 1,         # CharacterSpecificState.MOVING
    State.HARVESTING: 2,     # CharacterSpecificState.WORKING_1
    State.PLANTING: 3,       # CharacterSpecificState.WORKING_2
    State.WATERING: 4,       # CharacterSpecificState.WORKING_3
    State.COLLECTING: 5,     # CharacterSpecificState.COLLECTING
    State.STORING: 6,        # CharacterSpecificState.STORING
    State.CARRYING: 7,       # CharacterSpecificState.CARRYING
    State.RESTING: 8,        # CharacterSpecificState.RESTING
    State.FETCHING_WATER: 5, # CharacterSpecificState.COLLECTING
    State.CHOPPING: 2,       # CharacterSpecificState.WORKING_1
    State.PROCESSING_WOOD: 3, # CharacterSpecificState.WORKING_2
    # 渔夫状态映射
    State.FISHING: 3,        # CharacterSpecificState.WORKING_2 - 🔧 修复：添加FISHING状态映射
    State.CASTING: 2,        # CharacterSpecificState.WORKING_1
    State.REELING: 4,        # CharacterSpecificState.WORKING_3
    State.CATCHING: 5,       # CharacterSpecificState.COLLECTING
    # 矿工状态映射
    State.MINING: 2,         # CharacterSpecificState.WORKING_1
    State.SMELTING: 3,       # CharacterSpecificState.WORKING_2
    State.TRAVELING_UNDERGROUND: 1 # CharacterSpecificState.MOVING
}

## 工具方法 ##

# 状态分类检查
static func is_basic_state(state: State) -> bool:
    """检查是否为基础状态"""
    return state >= 0 and state <= 9

static func is_working_state(state: State) -> bool:
    """检查是否为工作状态"""
    return get_state_property(state, "is_working", false)

static func is_farmer_state(state: State) -> bool:
    """检查是否为农夫专用状态"""
    return state >= 20 and state <= 29

static func is_woodcutter_state(state: State) -> bool:
    """检查是否为伐木工专用状态"""
    return state >= 30 and state <= 39

static func is_fisherman_state(state: State) -> bool:
    """检查是否为渔夫专用状态"""
    return state >= 40 and state <= 49

static func is_miner_state(state: State) -> bool:
    """检查是否为矿工专用状态"""
    return state >= 60 and state <= 69

static func is_chef_state(state: State) -> bool:
    """检查是否为厨师专用状态"""
    return state >= 50 and state <= 59

# 状态属性获取
static func get_animation_for_state(state: State) -> String:
    """获取状态对应的动画名称"""
    return get_state_property(state, "animation", "idle")

static func get_state_category(state: State) -> String:
    """获取状态分类"""
    return get_state_property(state, "category", "basic")

static func get_state_property(state: State, property: String, default_value = null):
    """获取状态属性"""
    var properties = STATE_PROPERTIES.get(state, {})
    return properties.get(property, default_value)

# 状态组检查
static func is_state_in_group(state: State, group_name: String) -> bool:
    """检查状态是否属于指定组"""
    var group = STATE_GROUPS.get(group_name, [])
    return state in group

static func get_states_in_group(group_name: String) -> Array:
    """获取指定组的所有状态"""
    return STATE_GROUPS.get(group_name, [])

static func to_base_character_state(state: State) -> int:
    """转换为基类CharacterSpecificState值"""
    return BASE_CHARACTER_STATE_MAPPING.get(state, 0)

# 状态验证
static func is_valid_state(state: State) -> bool:
    """验证状态是否有效"""
    return state in STATE_PROPERTIES

static func is_valid_transition(from_state: State, to_state: State) -> bool:
    """验证状态转换是否有效"""
    # 基本规则：任何状态都可以转换到IDLE
    if to_state == State.IDLE:
        return true
    
    # 从IDLE可以转换到任何状态
    if from_state == State.IDLE:
        return true
    
    # 工作状态之间不能直接转换，必须先回到IDLE
    if is_working_state(from_state) and is_working_state(to_state):
        return false
    
    return true

# 调试方法
static func get_state_name(state: State) -> String:
    """获取状态的字符串名称"""
    for key in State:
        if State[key] == state:
            return key
    return "UNKNOWN"

static func get_debug_info(state: State) -> Dictionary:
    """获取状态的调试信息"""
    return {
        "state": state,
        "name": get_state_name(state),
        "category": get_state_category(state),
        "animation": get_animation_for_state(state),
        "is_working": is_working_state(state),
        "properties": STATE_PROPERTIES.get(state, {})
    }

static func should_trigger_ai_for_state(state: State) -> bool:
    """检查指定状态是否应该触发AI决策"""
    match state:
        State.IDLE, State.CARRYING, State.RESTING:
            return true
        State.MOVING:
            return false  # 移动时不触发AI
        _:
            return is_working_state(state) == false  # 非工作状态可以触发AI
