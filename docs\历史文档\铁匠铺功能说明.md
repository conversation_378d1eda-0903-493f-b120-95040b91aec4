# 铁匠铺功能说明

## 概述
铁匠铺是一个专门用于矿石熔炼和加工的建筑，玩家可以通过它将原始矿石转化为有用的成品。

## 功能特性

### 1. UI交互
- **唤出UI**: 点击铁匠铺建筑上的铁砧区域（右上角的小图标）来打开铁匠铺UI
- **关闭UI**: 点击UI右上角的"×"按钮或点击UI外部区域来关闭
- **紧凑设计**: UI设计紧凑，适合256像素高度的游戏场景

### 2. 矿石显示
- **自动加载**: 从`config/Ores.json`自动加载所有矿石类型
- **图标显示**: 每种矿石都显示对应的图标和中文名称
- **库存数量**: 实时显示玩家库存中每种矿石的数量
- **拖拽支持**: 支持将矿石从展示区域拖拽到优先级槽位

### 3. 优先级队列系统
- **4个优先级槽位**: 从左到右分别是优先级1（最高）到优先级4（最低）
- **拖拽放置**: 将矿石从矿石区域拖拽到优先级槽位来设置熔炼顺序
- **右键清除**: 右键点击优先级槽位可以清除其中的矿石
- **自动处理**: 铁匠铺会按照优先级顺序自动处理队列中的矿石

### 4. 熔炼系统
- **自动熔炼**: 当优先级队列中有矿石且铁匠铺空闲时，会自动开始熔炼
- **不同时间**: 不同矿石有不同的熔炼时间：
  - 煤炭: 3秒（最快）
  - 石头: 5秒
  - 铜矿石: 8秒
  - 铁矿石: 10秒
  - 银矿石: 12秒
  - 金矿石: 15秒
  - 宝石类: 18-20秒（最慢）
- **进度显示**: 实时显示当前熔炼进度和正在处理的矿石
- **自动产出**: 熔炼完成后，成品会自动添加到玩家库存

### 5. 成品转换
不同矿石会产生不同的成品：
- **石头** → 石砖 (x2)
- **煤炭** → 煤炭 (燃料，不变)
- **铁矿石** → 铁锭
- **金矿石** → 金锭
- **银矿石** → 银锭
- **铜矿石** → 铜锭
- **钻石矿石** → 钻石宝石
- **绿宝石矿石** → 绿宝石
- **红宝石矿石** → 红宝石
- **蓝宝石矿石** → 蓝宝石

## 使用方法

### 基本操作流程
1. **打开UI**: 点击铁匠铺上的铁砧图标
2. **查看矿石**: 在下方的矿石网格中查看可用的矿石
3. **设置优先级**: 将想要熔炼的矿石拖拽到上方的优先级槽位
4. **开始熔炼**: 铁匠铺会自动按照优先级顺序开始熔炼
5. **查看进度**: 在进度条区域查看当前熔炼状态
6. **收获成品**: 熔炼完成后，成品会自动添加到库存

### 高级技巧
- **批量处理**: 可以在优先级槽位中放置多种不同的矿石，铁匠铺会依次处理
- **优先级管理**: 将重要的矿石放在优先级1槽位，确保优先处理
- **库存管理**: 定期检查库存，确保有足够的原材料进行熔炼

## 技术实现

### 文件结构
- `scripts/buildings/types/BlacksmithBuilding.gd`: 铁匠铺建筑主逻辑
- `scripts/ui/BlacksmithUI.gd`: 铁匠铺UI控制器
- `scripts/ui/DraggableItemSlot.gd`: 支持拖拽的物品槽组件
- `scenes/Buildings/Blacksmith.tscn`: 铁匠铺建筑场景
- `scenes/UI/BlacksmithUI.tscn`: 铁匠铺UI场景
- `scenes/UI/DraggableItemSlot.tscn`: 可拖拽物品槽场景

### 关键特性
- **模块化设计**: UI和建筑逻辑分离，便于维护
- **数据驱动**: 矿石信息从JSON配置文件加载
- **事件驱动**: 使用信号系统进行组件间通信
- **自动化处理**: 智能队列管理和自动熔炼
- **用户友好**: 直观的拖拽操作和实时反馈

## 扩展可能性

### 未来功能
- **熔炼速度升级**: 可以通过升级提高熔炼速度
- **批量熔炼**: 支持一次熔炼多个相同矿石
- **配方系统**: 支持复杂的合成配方
- **工匠角色**: 添加工匠NPC来操作铁匠铺
- **视觉效果**: 添加火焰、烟雾等视觉效果

### 集成建议
- 与工匠角色系统集成，实现自动化操作
- 与制造UI集成，支持装备制作
- 与经济系统集成，支持成品交易

## 注意事项
- 确保`config/Ores.json`文件存在且格式正确
- 矿石图标需要正确配置在精灵表中
- 成品ID需要在资源管理器中正确注册
- UI位置会自动调整以避免超出屏幕边界
