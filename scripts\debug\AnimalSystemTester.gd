# AnimalSystemTester.gd
# 动物系统测试器 - 用于测试野生动物生成和驯养功能
extends Node

## 测试配置
var test_enabled: bool = true
var auto_test_interval: float = 5.0  # 自动测试间隔

## 引用
var game_manager = null
var wild_animal_spawner = null

func _ready() -> void:
    if not test_enabled:
        return
    
    name = "AnimalSystemTester"
    print("动物系统测试器已启动")
    
    # 延迟获取管理器引用
    call_deferred("_initialize_references")

func _initialize_references() -> void:
    """初始化管理器引用"""
    game_manager = get_node_or_null("/root/_GameManager")
    if not game_manager:
        print("测试器错误：无法找到GameManager")
        return
    
    # 等待GameManager初始化完成
    if not game_manager.is_initialized:
        await game_manager.game_initialized
    
    wild_animal_spawner = game_manager.get_wild_animal_spawner()
    if not wild_animal_spawner:
        print("测试器错误：无法找到WildAnimalSpawner")
        return
    
    print("动物系统测试器初始化完成")
    _setup_test_ui()

func _setup_test_ui() -> void:
    """设置测试UI"""
    # 创建简单的测试按钮
    var test_button = Button.new()
    test_button.text = "生成野生动物"
    test_button.position = Vector2(10, 10)
    test_button.size = Vector2(120, 30)
    test_button.pressed.connect(_on_spawn_test_pressed)
    
    var info_button = Button.new()
    info_button.text = "动物信息"
    info_button.position = Vector2(140, 10)
    info_button.size = Vector2(100, 30)
    info_button.pressed.connect(_on_info_test_pressed)
    
    var clear_button = Button.new()
    clear_button.text = "清除动物"
    clear_button.position = Vector2(250, 10)
    clear_button.size = Vector2(100, 30)
    clear_button.pressed.connect(_on_clear_test_pressed)

    var pen_button = Button.new()
    pen_button.text = "创建围栏"
    pen_button.position = Vector2(360, 10)
    pen_button.size = Vector2(100, 30)
    pen_button.pressed.connect(_on_create_pen_pressed)

    # 添加到场景
    get_tree().current_scene.add_child(test_button)
    get_tree().current_scene.add_child(info_button)
    get_tree().current_scene.add_child(clear_button)
    get_tree().current_scene.add_child(pen_button)

func _on_spawn_test_pressed() -> void:
    """测试生成野生动物"""
    if wild_animal_spawner:
        print("测试：开始强制生成野生动物...")
        wild_animal_spawner.force_spawn()
        print("测试：强制生成命令已发送")
    else:
        print("测试错误：野生动物生成器未找到")

func _on_info_test_pressed() -> void:
    """显示动物信息"""
    if wild_animal_spawner:
        var count = wild_animal_spawner.get_wild_animal_count()
        var weights = wild_animal_spawner.get_spawn_weights()
        print("当前野生动物数量: %d" % count)
        print("生成权重: %s" % weights)

func _on_clear_test_pressed() -> void:
    """清除所有野生动物"""
    if wild_animal_spawner:
        wild_animal_spawner.clear_all_wild_animals()
        print("测试：已清除所有野生动物")

func _input(event: InputEvent) -> void:
    """处理测试快捷键"""
    if not test_enabled:
        return
    
    if event is InputEventKey and event.pressed:
        match event.keycode:
            KEY_F1:
                _on_spawn_test_pressed()
            KEY_F2:
                _on_info_test_pressed()
            KEY_F3:
                _on_clear_test_pressed()
            KEY_F4:
                _test_taming_system()

func _test_taming_system() -> void:
    """测试驯养系统"""
    # 获取场景中的所有野生动物
    var animals = get_tree().get_nodes_in_group("animals")
    if animals.is_empty():
        print("测试：没有找到可驯养的动物")
        return
    
    # 找到第一个野生动物
    for animal in animals:
        if animal.get_meta("animal_state", 0) == 0:  # 0 = WILD
            print("测试：尝试驯养 %s" % animal.显示名称)
            _simulate_taming_clicks(animal)
            break

func _simulate_taming_clicks(animal: Animal) -> void:
    """模拟驯养点击"""
    var variant = animal.get_node("AnimalVariant")
    if not variant:
        print("测试错误：动物没有AnimalVariant组件")
        return
    
    # 模拟3次点击
    for i in range(3):
        var fake_event = InputEventMouseButton.new()
        fake_event.button_index = MOUSE_BUTTON_LEFT
        fake_event.pressed = true
        
        if variant.has_method("_on_taming_click"):
            variant._on_taming_click(null, fake_event, 0)
        
        await get_tree().create_timer(0.5).timeout
    
    print("测试：驯养点击模拟完成")

func _on_create_pen_pressed() -> void:
    """创建测试围栏"""
    # 使用场景文件创建围栏
    var pen_scene = preload("res://scenes/Buildings/AnimalPen.tscn")
    if pen_scene:
        var pen = pen_scene.instantiate()
        pen.name = "TestAnimalPen"
        pen.position = Vector2(400, 300)
        get_tree().current_scene.add_child(pen)
        print("测试：已创建动物围栏场景")
    else:
        print("测试错误：无法加载动物围栏场景")
