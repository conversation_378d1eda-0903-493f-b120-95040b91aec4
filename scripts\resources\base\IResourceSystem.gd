# IResourceSystem.gd
# 资源管理系统统一接口定义
class_name IResourceSystem
extends RefCounted

## 🎒 资源管理系统统一接口
## 定义所有资源相关操作的标准接口，确保一致性

## 核心接口 ##

# 资源数量管理
static func get_item_amount(item_id: String) -> int:
	"""获取物品数量
	@param item_id: 物品ID
	@return: 物品数量
	"""
	var resource_manager = get_resource_manager()
	if resource_manager and resource_manager.has_method("get_item_amount"):
		return resource_manager.get_item_amount(item_id)
	return 0

static func add_item(item_id: String, amount: int) -> bool:
	"""添加物品
	@param item_id: 物品ID
	@param amount: 添加数量
	@return: 是否成功添加
	"""
	var resource_manager = get_resource_manager()
	if resource_manager and resource_manager.has_method("add_item"):
		return resource_manager.add_item(item_id, amount)
	return false

static func remove_item(item_id: String, amount: int) -> bool:
	"""移除物品
	@param item_id: 物品ID
	@param amount: 移除数量
	@return: 是否成功移除
	"""
	var resource_manager = get_resource_manager()
	if resource_manager and resource_manager.has_method("remove_item"):
		return resource_manager.remove_item(item_id, amount)
	return false

static func can_afford_item(item_id: String, amount: int) -> bool:
	"""检查是否有足够的物品
	@param item_id: 物品ID
	@param amount: 需要数量
	@return: 是否有足够数量
	"""
	return get_item_amount(item_id) >= amount

# 批量操作
static func add_multiple_items(items: Dictionary) -> bool:
	"""批量添加物品
	@param items: 物品字典 {item_id: amount}
	@return: 是否全部成功添加
	"""
	var resource_manager = get_resource_manager()
	if not resource_manager:
		return false
	
	var all_success = true
	for item_id in items:
		var amount = items[item_id]
		if not add_item(item_id, amount):
			all_success = false
	
	return all_success

static func remove_multiple_items(items: Dictionary) -> bool:
	"""批量移除物品
	@param items: 物品字典 {item_id: amount}
	@return: 是否全部成功移除
	"""
	# 先检查是否有足够的物品
	for item_id in items:
		var amount = items[item_id]
		if not can_afford_item(item_id, amount):
			return false
	
	# 执行移除
	var all_success = true
	for item_id in items:
		var amount = items[item_id]
		if not remove_item(item_id, amount):
			all_success = false
	
	return all_success

static func can_afford_multiple_items(items: Dictionary) -> bool:
	"""检查是否有足够的多种物品
	@param items: 物品字典 {item_id: amount}
	@return: 是否全部有足够数量
	"""
	for item_id in items:
		var amount = items[item_id]
		if not can_afford_item(item_id, amount):
			return false
	return true

# 物品类型管理
static func get_items_by_type(item_type: int) -> Dictionary:
	"""根据类型获取物品
	@param item_type: 物品类型（ResourceManager.ItemType枚举）
	@return: 物品字典
	"""
	var resource_manager = get_resource_manager()
	if resource_manager and resource_manager.has_method("get_items_by_type"):
		return resource_manager.get_items_by_type(item_type)
	return {}

static func get_all_items() -> Dictionary:
	"""获取所有物品
	@return: 所有物品的字典
	"""
	var resource_manager = get_resource_manager()
	if resource_manager and resource_manager.has_method("get_all_items"):
		return resource_manager.get_all_items()
	return {}

# 存储管理
static func add_item_to_storage(storage: Node, item_id: String, amount: int) -> bool:
	"""向存储添加物品
	@param storage: 存储节点
	@param item_id: 物品ID
	@param amount: 数量
	@return: 是否成功
	"""
	if not is_instance_valid(storage):
		return false
	
	if storage.has_method("add_item"):
		return storage.add_item(item_id, amount)
	elif storage.has_method("store_item"):
		return storage.store_item(item_id, amount)
	
	return false

static func remove_item_from_storage(storage: Node, item_id: String, amount: int) -> bool:
	"""从存储移除物品
	@param storage: 存储节点
	@param item_id: 物品ID
	@param amount: 数量
	@return: 是否成功
	"""
	if not is_instance_valid(storage):
		return false
	
	if storage.has_method("remove_item"):
		return storage.remove_item(item_id, amount)
	elif storage.has_method("take_item"):
		return storage.take_item(item_id, amount)
	
	return false

static func get_storage_item_amount(storage: Node, item_id: String) -> int:
	"""获取存储中的物品数量
	@param storage: 存储节点
	@param item_id: 物品ID
	@return: 数量
	"""
	if not is_instance_valid(storage):
		return 0
	
	if storage.has_method("get_item_amount"):
		return storage.get_item_amount(item_id)
	elif storage.has_method("get_stored_amount"):
		return storage.get_stored_amount(item_id)
	
	return 0

# 货币管理
static func get_money() -> int:
	"""获取当前金钱数量"""
	var resource_manager = get_resource_manager()
	if resource_manager and resource_manager.has_method("get_money"):
		return resource_manager.get_money()
	return 0

static func add_money(amount: int) -> bool:
	"""添加金钱"""
	var resource_manager = get_resource_manager()
	if resource_manager and resource_manager.has_method("add_money"):
		return resource_manager.add_money(amount)
	return false

static func remove_money(amount: int) -> bool:
	"""移除金钱"""
	var resource_manager = get_resource_manager()
	if resource_manager and resource_manager.has_method("remove_money"):
		return resource_manager.remove_money(amount)
	return false

static func can_afford_money(amount: int) -> bool:
	"""检查是否有足够金钱"""
	return get_money() >= amount

## 工具方法 ##

# 统一的资源管理器获取
static func get_resource_manager():
	"""获取资源管理器实例"""
	var game_manager = _get_game_manager()
	if game_manager and game_manager.has_method("get_resource_manager"):
		return game_manager.get_resource_manager()
	return null

# 统一的GameManager获取方法
static func _get_game_manager():
	"""获取GameManager实例"""
	var scene_tree = Engine.get_main_loop() as SceneTree
	if not scene_tree:
		return null

	# 尝试从根节点获取
	var root_node = scene_tree.root
	if root_node:
		var game_manager = root_node.get_node_or_null("_GameManager")
		if game_manager:
			return game_manager

	# 尝试从当前场景查找
	var current_scene = scene_tree.current_scene
	if current_scene:
		var game_manager = current_scene.find_child("_GameManager", true, false)
		if game_manager:
			return game_manager

	return null

# 物品验证
static func is_valid_item_id(item_id: String) -> bool:
	"""验证物品ID是否有效"""
	if item_id.is_empty():
		return false
	
	var resource_manager = get_resource_manager()
	if not resource_manager:
		return false
	
	# 检查是否在任何物品类型中存在
	for item_type in ResourceManager.ItemType.values():
		var items_dict = get_items_by_type(item_type)
		if items_dict.has(item_id):
			return true
	
	return false

# 调试和日志
static func log_resource_operation(operation: String, item_id: String, amount: int, success: bool) -> void:
	"""记录资源操作日志"""
	var status = "成功" if success else "失败"
	print("[IResourceSystem] %s: %s x%d - %s" % [operation, item_id, amount, status])

## 兼容性方法 ##

# 为了向后兼容，提供一些常用的别名方法
static func has_item(item_id: String, amount: int = 1) -> bool:
	"""检查是否有物品（别名方法）"""
	return can_afford_item(item_id, amount)

static func give_item(item_id: String, amount: int) -> bool:
	"""给予物品（别名方法）"""
	return add_item(item_id, amount)

static func take_item(item_id: String, amount: int) -> bool:
	"""拿取物品（别名方法）"""
	return remove_item(item_id, amount)
