## 总体计划：树木生长与伐木工系统实现 (参照农业系统架构)

我们将参照农业系统的实际实现方式和脚本组织结构，分阶段开发林业系统。核心目标是实现动态的树木生长、砍伐以及伐木工角色的自动化工作流程，同时确保新系统与现有项目架构的无缝集成和一致性。

**核心架构遵循 (参照农业系统模式):**

```
scripts/
├── forestry/                    # 林业系统核心对象目录
│   ├── Tree.gd                 # 树木基类 (数据容器、视觉状态、基础交互响应)
│   ├── WoodSource.gd           # 木材源对象 (树木砍伐后产生的可收集木材堆)
│   └── (未来其他林业核心对象，如特殊树种的派生类等)
│
├── forestry/managers/           # 林业系统专属管理器目录
│   ├── TreeLifecycleManager.gd # 负责树木的生成、生长阶段驱动、再生逻辑
│   ├── WoodcutterTaskManager.gd# 负责伐木工的AI决策、任务分配与流程管理
│   └── WoodSourceManager.gd    # 负责管理和追踪所有WoodSource实例
│
├── characters/
│   └── types/
│       └── Woodcutter.gd       # 伐木工角色脚本 (行为执行单元)
│
└── constants/
    └── ForestryTypes.gd        # 林业系统相关枚举和常量
```

**配置文件：**
*   `config/forestry.json`: 统一管理树木类型数据、生长规则、木材源属性、林业系统参数，并可扩展加入伐木工相关配置。

**场景结构规范：**
*   `scenes/Objects/Trees/Tree.tscn`: 基础树木场景，定义所有树木共享的节点结构
    *   根节点: `Node2D` (附加 `Tree.gd`)
    *   子节点:
        *   `Sprite2D`: 树木视觉表现
        *   `CollisionShape2D`: 碰撞形状，用于交互检测
        *   `BottomPoint (Marker2D)`: 定义树木的底部锚点，用于Y排序和网格对齐
        *   `InteractionPoints (Node2D)`: 包含多个 `Marker2D` 子节点，定义伐木工可交互的位置
        *   `HealthDisplay (Optional)`: 可选的生命值显示
*   `scenes/Objects/Trees/OakTree.tscn`, `PineTree.tscn`: 继承自 `Tree.tscn` 的特定树木类型
*   `scenes/Objects/Forestry/WoodSource.tscn`: 木材源场景
    *   根节点: `Node2D` (附加 `WoodSource.gd`)
    *   子节点:
        *   `Sprite2D`: 木材堆视觉表现
        *   `CollisionShape2D`: 交互区域
        *   `AmountDisplay (Optional)`: 可选的剩余资源量显示

---

**阶段一：林业核心框架与基础对象搭建**

此阶段目标是搭建林业系统的基础框架，定义核心数据结构和对象，并实现各专属管理器的基本骨架。

1.  **定义 `ForestryTypes.gd`:**
    *   **路径**: `scripts/constants/ForestryTypes.gd`
    *   **内容**: 定义林业相关的枚举，例如：
        *   `enum TreeType { OAK, PINE, ... }`
        *   `enum TreeGrowthStage { SAPLING, YOUNG, MATURE, STUMP }`
        *   `enum WoodcutterTaskType { FIND_TREE, MOVE_TO_TREE, CHOP_ACTION, MOVE_TO_WOOD_SOURCE, COLLECT_ACTION, MOVE_TO_STORAGE, STORE_ACTION, REST, RANDOM_WALK }` (供 `WoodcutterTaskManager` 使用)
        *   `enum WoodSourceState { AVAILABLE, EMPTY }`

2.  **设计并创建 `forestry.json`:**
    *   **路径**: `config/forestry.json`
    *   **目标**: 统一管理林业系统配置。
    *   **内容结构示例**:
            ```json
        {
          "tree_types": [
              {
              "id": "oak_tree", // ForestryTypes.TreeType.OAK 对应
                "display_name": "橡树",
              "scene_path": "res://scenes/objects/trees/OakTree.tscn",
              "max_health": 100,
              "resource_yield_on_fell": { "item_id": "wood", "min_amount": 3, "max_amount": 5 }, // 砍倒后产生的WoodSource的初始量
              "stump_duration_seconds": 10, // 树桩状态持续时间，之后可被TreeLifecycleManager处理用于再生或移除
              "regrowth_time_seconds": 300, // 从树桩消失到新树苗出现的时间 (如果can_regrow_as_new_sapling)
              "can_regrow_as_new_sapling": true, // 是否在树桩消失后由TreeLifecycleManager重新种树苗
                "collision_size_grids": {"x": 1, "y": 1},
                "bottom_point_offset": {"x": 16, "y": 32},
              "interaction_points": [{"x": 0, "y": 24, "id": "south_face"}],
              "growth_stages": [
                {"stage_key": "SAPLING", "duration_seconds": 60, "sprite_path": "res://...", "scale": {}, "next_stage_key": "YOUNG"},
                {"stage_key": "YOUNG", "duration_seconds": 120, "sprite_path": "res://...", "scale": {}, "next_stage_key": "MATURE"},
                {"stage_key": "MATURE", "duration_seconds": 0, "sprite_path": "res://...", "scale": {}, "is_harvestable": true, "felled_stage_key": "STUMP"},
                {"stage_key": "STUMP", "duration_seconds": 0, "sprite_path": "res://...", "scale": {}} // 砍倒后进入此阶段
              ],
              "allowed_tile_custom_data": [{"layer_name": "Ground", "custom_data_key": "can_grow_trees", "expected_value": true}]
            }
          ],
          "wood_source_settings": {
            "scene_path": "res://scenes/objects/forestry/WoodSource.tscn",
            "collection_per_action": 1 // 伐木工每次收集动作能从WoodSource收集多少
          },
          "woodcutter_settings": { // 可用于初始化Woodcutter.gd或供WoodcutterTaskManager参考
            "chop_damage_per_action": 25, // 每次砍伐动作造成的伤害
            "stamina_cost_chop": 10,
            "stamina_cost_collect": 5,
            "default_cutting_time": 2.0, // 砍伐一次动作的时间
            "default_collecting_time": 1.5 // 收集一次动作的时间
          },
          "general_forestry_settings": {
            "max_trees_per_chunk": 30, // TreeLifecycleManager的树木密度控制
            "global_growth_tick_interval": 10.0, // TreeLifecycleManager生长逻辑更新频率
            "tree_spawn_rules": { // TreeLifecycleManager生成树木的规则
                "min_distance_between_trees": 2.0 // 网格单位
            }
          }
        }
        ```

3.  **扩展 `DataManager.gd`:**
    *   添加方法以读取和提供 `forestry.json` 中的数据，例如：
        *   `get_tree_type_config(tree_type_id: String) -> Dictionary`
        *   `get_wood_source_config() -> Dictionary`
        *   `get_forestry_general_settings() -> Dictionary`
        *   `get_woodcutter_action_settings() -> Dictionary`

4.  **实现 `Tree.gd` (位于 `scripts/forestry/Tree.gd`):**
    *   **职责**: 数据容器，表示单棵树的当前状态、类型和视觉。响应伤害，并在生命耗尽时转换为树桩状态。
        *   `Node2D` 基类。
        *   `@export var tree_type_id: String` (或使用 `ForestryTypes.TreeType` 枚举)。
    *   私有变量: `_config: Dictionary`, `current_health: int`, `current_stage_key: String`.
    *   节点引用: `Sprite2D`, `CollisionShape2D`, `BottomPoint (Marker2D)`, `InteractionPointsContainer (Node2D)`.
    *   `initialize(type_id: String, initial_stage_key: String)`: 由 `TreeLifecycleManager` 调用，加载配置，设置初始生命值、视觉。
    *   `update_visuals(stage_config: Dictionary)`: 根据阶段配置更新 `Sprite2D`。
    *   `set_growth_stage(new_stage_key: String)`: 由 `TreeLifecycleManager` 调用，更新当前阶段并调用 `update_visuals`。
    *   `take_damage(amount: int) -> bool`: 减少生命值。如果生命值 <= 0，将 `current_stage_key` 设为 "STUMP"，调用 `update_visuals`，发出 `became_stump` 信号，返回 `true` (表示被砍倒)。否则返回 `false`。
    *   `is_harvestable() -> bool`: 根据当前阶段和配置判断是否可砍伐。
    *   信号: `health_changed(new_health, max_health)`, `became_stump(tree_instance: Tree)`.

5.  **实现 `WoodSource.gd` (位于 `scripts/forestry/WoodSource.gd`):**
    *   **职责**: 代表树木被砍伐后地面上可收集的木材堆。
    *   `Node2D` 基类。
    *   私有变量: `_config: Dictionary` (来自`forestry.json`的`wood_source_settings`), `current_wood_amount: int`.
    *   节点引用: `Sprite2D`, `CollisionShape2D` (可选，用于交互范围)。
    *   `initialize(initial_amount: int, position: Vector2)`: 由 `WoodSourceManager` 调用，设置初始木材量和视觉。
    *   `collect_wood(requested_amount: int) -> int`: 减少 `current_wood_amount`，返回实际收集量。如果 `current_wood_amount` <= 0，发出 `emptied` 信号。
    *   `get_remaining_wood() -> int`.
    *   信号: `wood_amount_changed(new_amount: int)`, `emptied(source_instance: WoodSource)`.

6.  **实现 `TreeLifecycleManager.gd` (位于 `scripts/forestry/managers/TreeLifecycleManager.gd`):**
    *   **职责**: 负责在允许的区域自动生成新树木，驱动所有树木实例的生长阶段变化，以及处理树桩的再生或移除。
    *   持有所有活动树木实例的引用 (`_active_trees: Array[Tree]`).
    *   `_ready()`: 获取 `TerrainTileMap`, `GridAreaManager`, `WoodSourceManager` 引用。加载林业配置。启动生长更新计时器。
    *   `_on_growth_tick()`: 遍历 `_active_trees`，根据配置和时间更新其生长阶段 (调用 `tree.set_growth_stage()`)。
    *   `_attempt_spawn_new_trees()`: (可定期调用或基于游戏事件) 根据 `forestry.json` 中的密度和规则，在合适的地块（查询`TerrainTileMap`自定义数据和`GridAreaManager`占用情况）尝试生成新的 `Tree` 实例 (调用 `tree.initialize()`)。
    *   `_on_tree_became_stump(tree: Tree)`: (连接到 `Tree.became_stump` 信号)
        *   获取树的配置，特别是 `resource_yield_on_fell`。
        *   调用 `WoodSourceManager.create_wood_source_at(tree.global_position, yield_config.item_id, randi_range(yield_config.min_amount, yield_config.max_amount))`。
        *   如果树的配置 `can_regrow_as_new_sapling` 为 `true`，则在 `stump_duration_seconds` 后，再等待 `regrowth_time_seconds`，然后尝试在原位置或附近重新生成一个树苗（移除旧树桩实例，创建新树实例）。或者，树桩本身在一定时间后消失，然后此管理器负责重新种植。
    *   与 `GridAreaManager` 交互: 树木生成/移除时更新占用。

7.  **实现 `WoodSourceManager.gd` (位于 `scripts/forestry/managers/WoodSourceManager.gd`):**
    *   **职责**: 管理场景中所有的 `WoodSource` 实例。
    *   持有所有活动木材源实例的引用 (`_active_wood_sources: Array[WoodSource]`).
    *   `create_wood_source_at(position: Vector2, item_id: String, initial_amount: int) -> WoodSource`: 实例化 `WoodSource.tscn`，调用其 `initialize()`，添加到场景树和 `_active_wood_sources`。
    *   `_on_wood_source_emptied(source: WoodSource)`: (连接到 `WoodSource.emptied` 信号) 从场景和 `_active_wood_sources` 中移除该实例。
    *   `find_nearest_available_wood_source(position: Vector2, min_amount: int = 1) -> WoodSource`: 查询 `_active_wood_sources` 找到符合条件的最近木材源。

8. **实现 `WoodcutterTaskManager.gd` (位于 `scripts/forestry/managers/WoodcutterTaskManager.gd`):**
    *   **职责**: 管理所有伐木工角色，分配AI任务，协调工作流程。
    *   持有所有已注册伐木工实例的引用 (`_registered_woodcutters: Array[Woodcutter]`).
    *   引用 `TreeLifecycleManager` (获取可砍伐树木、树木交互点), `WoodSourceManager` (获取可收集木材堆), 以及全局的仓库管理器（用于查找存储点）。
    *   **核心AI决策与任务分配逻辑 (`_decide_next_task_for_woodcutter(woodcutter: Woodcutter)`)**:
        1.  **优先存储**: 如果 `woodcutter.inventory` 中有木材:
            *   查找最近的可用仓库。
            2.  如果找到仓库，则分配 `assign_task_store` (先移动到仓库交互点)。
            3.  任务分配后返回。
        2.  **其次收集现有木材堆**: 如果伐木工背包为空（或刚完成存储）:
            *   调用 `WoodSourceManager.find_nearest_available_wood_source(woodcutter.global_position)`。
            *   如果找到 `WoodSource`，则分配 `assign_task_collect` (先移动到其交互点)。
            *   任务分配后返回。
        3.  **然后砍伐成熟树木**: 如果伐木工背包为空，且没有可直接收集的木材堆:
            *   调用 `TreeLifecycleManager.find_nearest_harvestable_tree(woodcutter.global_position)` (此方法需在 `TreeLifecycleManager` 中实现，返回一个 `Tree` 实例及其推荐的交互点，或者只返回 `Tree`，交互点由本管理器选择)。
            *   选择树木交互点时，应考虑树木大小和伐木工位置，确保交互合理性（例如，对于大树，可以选择左右两侧的交互点，而不是仅限底部）。
            *   如果找到可砍伐的树，则分配 `assign_task_chop` (先移动到选定的树木交互点)。
            *   任务分配后返回。
        4.  **最后闲置/随机行走**: 如果以上条件均不满足（无木材需存储，无木材堆可收集，无成熟树木可砍伐）:
            *   分配 `assign_task_idle` 或 `assign_task_random_walk`。
            *   任务分配后返回。
    *   连接到 `Woodcutter.task_completed` 信号，根据完成的任务类型和结果，决定是否需要为该伐木工分配后续任务（例如，砍树成功后，新生成的 `WoodSource` 应该能在下一次决策中被优先收集。
    *   `register_woodcutter(woodcutter: Woodcutter)` / `unregister_woodcutter(woodcutter: Woodcutter)`.

9. **`GameManager` 集成:**
    *   在 `GameManager._ready()` 中实例化 `TreeLifecycleManager`, `WoodSourceManager`, `WoodcutterTaskManager`。
    *   这些管理器可以通过 `GameManager` 提供给其他需要访问它们的系统（如 `CharacterManager` 在生成伐木工后需要注册到 `WoodcutterTaskManager`）。

**完整信号系统：**

1. **Tree.gd 信号：**
   * `health_changed(new_health: int, max_health: int)` 
     - 连接者：UI系统（如果需要显示树木健康状态）
     - 触发：当树木受到伤害但尚未倒下时
   * `became_stump(tree_instance: Tree)` 
     - 连接者：`TreeLifecycleManager`
     - 触发：当树木生命值降至0，变为树桩时
     - 处理：生成木材堆，更新占用状态，安排再生

2. **WoodSource.gd 信号：**
   * `wood_amount_changed(new_amount: int)` 
     - 连接者：UI系统（如果需要显示剩余资源量）
     - 触发：当木材被收集时
   * `emptied(source_instance: WoodSource)` 
     - 连接者：`WoodSourceManager`
     - 触发：当木材堆被完全收集空时
     - 处理：从场景和管理列表中移除木材堆实例

3. **Woodcutter.gd 信号：**
   * `task_completed(woodcutter: Woodcutter, task_type: int, success: bool, details: Dictionary)` 
     - 连接者：`WoodcutterTaskManager`
     - 触发：当伐木工完成任何任务时
     - 处理：根据任务类型和结果决定下一步操作
   * `request_new_task(woodcutter: Woodcutter)` 
     - 连接者：`WoodcutterTaskManager`
     - 触发：当伐木工空闲且需要新任务时
     - 处理：为伐木工分配新任务
   * `inventory_changed(item_id: String, new_amount: int)` 
     - 连接者：UI系统（如果需要显示伐木工物品栏）
     - 触发：当伐木工收集或存储资源时

**区域管理集成细节：**

1. **树木生成与区域占用**
   * `TreeLifecycleManager` 在生成新树木前，会通过 `GridAreaManager` 检查：
     - 地块是否已被其他实体（如建筑、其他树木）占用
     - 地块是否距离其他树木足够远（基于`min_distance_between_trees`配置）
     - 地块是否在农田或其他不适合生长树木的区域内
   * 生成树木后，立即向 `GridAreaManager` 注册占用：
     ```gdscript
     # 在TreeLifecycleManager中
     func _register_tree_occupation(tree: Tree, grid_pos: Vector2i):
         var tree_config = _data_manager.get_tree_type_config(tree.tree_type_id)
         var size = tree_config.collision_size_grids
         _grid_area_manager.register_area_occupation(
             grid_pos, 
             Vector2i(size.x, size.y),
             AreaTypes.Type.RESOURCE_NODE_OCCUPIED,
             tree.get_instance_id(),
             tree
         )
     ```

2. **不同生长阶段的占用处理**
   * 树苗(`SAPLING`)和年轻树(`YOUNG`)：注册为`RESOURCE_NODE_OCCUPIED`，但配置允许被建筑覆盖
   * 成熟树(`MATURE`)：注册为`RESOURCE_OBSTRUCTION`，阻止建筑放置
   * 树桩(`STUMP`)：注册为`RESOURCE_NODE_OCCUPIED`，允许被建筑覆盖，但阻止新树木生成
   * 实现示例：
     ```gdscript
     # 在TreeLifecycleManager中
     func _update_tree_occupation_type(tree: Tree):
         var grid_pos = Utilities.world_to_grid(tree.global_position, _grid_size)
         var tree_config = _data_manager.get_tree_type_config(tree.tree_type_id)
         var size = tree_config.collision_size_grids
         
         # 根据生长阶段决定占用类型
         var occupation_type = AreaTypes.Type.RESOURCE_NODE_OCCUPIED
         if tree.current_stage_key == "MATURE":
             occupation_type = AreaTypes.Type.RESOURCE_OBSTRUCTION
         
         # 更新GridAreaManager中的占用类型
         _grid_area_manager.update_area_occupation_type(
             grid_pos,
             Vector2i(size.x, size.y),
             occupation_type,
             tree.get_instance_id()
         )
     ```

3. **木材堆与区域占用**
   * 木材堆(`WoodSource`)注册为临时占用，类型为`RESOURCE_NODE_OCCUPIED`
   * 木材堆被完全收集后，自动从`GridAreaManager`中注销占用
   * 实现示例：
     ```gdscript
     # 在WoodSourceManager中
     func _on_wood_source_emptied(source: WoodSource):
         var grid_pos = Utilities.world_to_grid(source.global_position, _grid_size)
         _grid_area_manager.unregister_area_occupation(
             grid_pos,
             Vector2i(1, 1), # 假设木材堆占用1x1格子
             source.get_instance_id()
         )
         source.queue_free()
         _active_wood_sources.erase(source)
     ```

4. **与农业系统的区域协调**
   * `TreeLifecycleManager`的树木生成逻辑会避开已被`Farmland`占用的区域
   * 通过`GridAreaManager`进行查询，确保不会在农田上生成树木
   * 反过来，`BuildingManager`在放置农田时也会检查区域是否被树木占用

**Y排序与可视化深度**

1. **确保正确的视觉深度**
   * 所有树木实例将被添加到场景中启用了Y排序的节点下
   * 树木的`BottomPoint`用于确定Y排序的参考点，确保角色能正确地走到树木前后
   * 实现示例：
     ```gdscript
     # 在TreeLifecycleManager中
     func _spawn_tree_at(position: Vector2, tree_type_id: String) -> Tree:
         var tree_scene = load(_data_manager.get_tree_type_config(tree_type_id).scene_path)
         var tree_instance = tree_scene.instantiate()
         _game_manager.get_y_sort_layer().add_child(tree_instance)
         
         # 设置位置，使BottomPoint对齐到传入的位置
         var bottom_point = tree_instance.get_node("BottomPoint")
         tree_instance.global_position = position - bottom_point.position
         
         tree_instance.initialize(tree_type_id, "SAPLING")
         return tree_instance
     ```

2. **视觉反馈**
   * 树木的不同生长阶段有不同的外观（通过`update_visuals`方法更新）
   * 被砍伐的动作有明显的视觉反馈（如树木晃动效果）
   * 树桩阶段使用单独的精灵图像，清晰表示树木已被砍伐

---

**阶段二：树木与木材源视觉及基础交互实现**

1.  **TileMap 地块配置 (`Tileset.tres`):** (同前)
    *   为 "Ground" 图层的草地等地块添加自定义数据 `can_grow_trees: true`。

2.  **创建/重构场景:**
    *   `Tree.tscn` (及派生如 `OakTree.tscn`, `PineTree.tscn`): 确保场景结构符合 `Tree.gd` 的节点需求。在检查器中设置各自的 `tree_type_id`。
    *   `WoodSource.tscn`: 创建木材堆的场景，包含 `Sprite2D` 和必要的脚本。

3.  **初步生成与状态测试:**
    *   在 `TreeLifecycleManager` 中实现一个测试函数手动调用 `_attempt_spawn_new_trees()`。
    *   测试树木能否正确显示、`BottomPoint` 等是否正确。
    *   手动调用 `Tree.take_damage()` 直至其变为树桩，测试 `became_stump` 信号是否触发。
    *   测试 `TreeLifecycleManager` 在接收到 `became_stump` 后能否正确调用 `WoodSourceManager` 创建 `WoodSource` 实例。
    *   测试 `GridAreaManager` 是否正确记录占用。

---

**阶段三：树木完整生命周期与木材源收集逻辑实现**

1.  **完善 `TreeLifecycleManager.gd`:**
    *   完整实现 `_on_growth_tick()`，驱动树木从树苗到成熟的阶段变化，正确调用 `tree.set_growth_stage()`。
    *   完整实现树桩再生逻辑：当树桩满足再生条件和时间后，移除旧树桩或将其变回树苗，并重新开始生长。

2.  **完善 `WoodSource.gd` 和 `WoodSourceManager.gd`:**
    *   完整实现 `WoodSource.collect_wood()` 及其 `emptied` 信号。
    *   确保 `WoodSourceManager` 在收到 `emptied` 信号后能正确移除 `WoodSource` 实例。
    *   实现 `WoodSourceManager.find_nearest_available_wood_source()`。

---

**阶段四：伐木工角色与任务系统实现**

此阶段的核心是实现伐木工角色的具体行为逻辑和其任务调度管理。

1.  **重构/实现 `Woodcutter.gd` (位于 `scripts/characters/types/Woodcutter.gd`):**
    *   **目标**: 继承 `Character.gd` (与农夫使用相同的基类，以复用基础属性和功能)，作为行为执行单元，响应 `WoodcutterTaskManager` 的指令。**移除所有体力（Stamina）相关的属性和逻辑。**
    *   **主要属性**: `current_task_type`, `current_task_target`, `inventory` (用于存放收集的木材), 动作计时参数 (如 `cutting_time`, `collecting_time`，这些参数应从 `forestry.json` 加载或在任务分配时传入)。
    *   **信号**: `task_completed(woodcutter, task_type, success, details)`, `request_new_task(woodcutter)` (与现有角色信号保持一致)。
    *   **任务接收方法 (由 `WoodcutterTaskManager` 调用)**:
        *   `assign_task_move_to(target_pos: Vector2, next_action_on_arrival: ForestryTypes.WoodcutterTaskType, task_data: Dictionary = {})`
        *   `assign_task_chop(tree: Tree, interaction_point: Marker2D)`: 移动到指定的树木交互点进行砍伐。
        *   `assign_task_collect(wood_source: WoodSource, interaction_point: Marker2D)`: 移动到指定的木材堆交互点进行收集。**此过程的动画和逻辑应参考农夫收集作物的实现。**
        *   `assign_task_store(storage_building: Node, item_id: String, amount: int, interaction_point: Marker2D)`: 移动到指定的仓库交互点进行存储。**此过程的动画和逻辑应参考农夫存储作物的实现。**
        *   `assign_task_rest(duration: float)` (如果需要，但目前无体力系统，可考虑移除或改为纯粹的空闲等待)
        *   `assign_task_random_walk()`
        *   `assign_task_idle(duration: float)`
    *   **核心行为逻辑**:
        *   `_on_movement_completed(success: bool)`: (连接 `Character.gd` 的移动完成信号) 若成功，根据 `next_action_on_arrival` 执行相应动作（砍伐、收集、存储），启动相应计时器和动画。
        *   `_on_action_timer_timeout()`: 动作完成。
            *   **CHOP_ACTION**: 对目标 `Tree` 实例造成伤害 (通过调用 `tree.take_damage()`)。发出 `task_completed` (details包含是否砍倒，目标树)。
            *   **COLLECT_ACTION**: 调用 `WoodSource.collect_wood()`，更新伐木工背包。发出 `task_completed`。
            *   **STORE_ACTION**: 调用仓库的存储方法，清空或减少背包中对应物品。发出 `task_completed`。
            *   **IDLE/REST**: 发出 `task_completed`。
        *   `_complete_current_task(success: bool, details: Dictionary = {})`: 清理当前任务状态，发出 `task_completed` 信号，若当前无后续指令且AI允许，则发出 `request_new_task` 信号。
    *   **动画和视觉**:
        *   根据当前动作和状态更新动画播放器（例如，`walk`, `idle`, `chop_action`, `collect_action`, `store_action` 动画）。`collect_action` 和 `store_action` 的动画应与农夫一致。
        *   头顶状态图标可根据当前任务类型更新。
        *   树木交互点选择**:
        *   当执行 `CHOP_ACTION` 时，`WoodcutterTaskManager` 会为其选定一个合适的 `tree.get_node("InteractionPoints").get_children()` 中的一个 `Marker2D` 作为目标。
        *   对于 `WoodSource` 和存储建筑的交互，将使用其预定义的、与农夫系统兼容的交互点。

2.  **完善 `WoodcutterTaskManager.gd`:**
    *   **职责**: 管理所有伐木工角色，根据新的AI决策逻辑分配任务，协调工作流程。**移除所有体力相关的检查和逻辑。**
    *   持有所有已注册伐木工实例的引用 (`_registered_woodcutters: Array[Woodcutter]`)。
    *   引用 `TreeLifecycleManager` (获取可砍伐树木、树木交互点), `WoodSourceManager` (获取可收集木材堆), 以及全局的仓库管理器（用于查找存储点）。
    *   **核心AI决策与任务分配逻辑 (`_decide_next_task_for_woodcutter(woodcutter: Woodcutter)`)**:
        1.  **优先存储**: 如果 `woodcutter.inventory` 中有木材:
            *   查找最近的可用仓库。
            2.  如果找到仓库，则分配 `assign_task_store` (先移动到仓库交互点)。
            3.  任务分配后返回。
        2.  **其次收集现有木材堆**: 如果伐木工背包为空（或刚完成存储）:
            *   调用 `WoodSourceManager.find_nearest_available_wood_source(woodcutter.global_position)`。
            *   如果找到 `WoodSource`，则分配 `assign_task_collect` (先移动到其交互点)。
            *   任务分配后返回。
        3.  **然后砍伐成熟树木**: 如果伐木工背包为空，且没有可直接收集的木材堆:
            *   调用 `TreeLifecycleManager.find_nearest_harvestable_tree(woodcutter.global_position)` (此方法需在 `TreeLifecycleManager` 中实现，返回一个 `Tree` 实例及其推荐的交互点，或者只返回 `Tree`，交互点由本管理器选择)。
            *   选择树木交互点时，应考虑树木大小和伐木工位置，确保交互合理性（例如，对于大树，可以选择左右两侧的交互点，而不是仅限底部）。
            *   如果找到可砍伐的树，则分配 `assign_task_chop` (先移动到选定的树木交互点)。
            *   任务分配后返回。
        4.  **最后闲置/随机行走**: 如果以上条件均不满足（无木材需存储，无木材堆可收集，无成熟树木可砍伐）:
            *   分配 `assign_task_idle` 或 `assign_task_random_walk`。
            *   任务分配后返回。
    *   **伐木工生成**: 伐木工应通过UI中的 `CharacterPanel` 内的 `LumberingButton` 生成，生成后自动注册到 `WoodcutterTaskManager`。
    *   处理 `Woodcutter.task_completed` 信号：
        *   任务完成后，立即为该伐木工调用 `_decide_next_task_for_woodcutter()` 来决定新任务。例如，砍伐成功后，新生成的 `WoodSource` 应该能在下一次决策中被优先收集。
        *   任务失败（如目标消失），也应重新决策。
    *   管理伐木工的注册/注销 (`register_woodcutter`, `unregister_woodcutter`)。

3.  **工具与动画:**
    *   确保伐木工有对应的砍伐动画 (`chop_action`)。
    *   收集 (`collect_action`) 和存储 (`store_action`) 动画复用农夫的动画资源。
    *   行走 (`walk`) 和空闲 (`idle`) 动画也应具备。

---

**阶段五：集成、测试与迭代** (内容基本不变，但测试的具体系统会是新的架构)

1.  **全面测试所有林业系统功能:**
    *   树木生成、按阶段生长、视觉变化。
    *   `GridAreaManager` 对树木、树桩的占用和释放。
    *   树木被砍伐后 `WoodSource` 的正确生成。
    *   伐木工能否被 `WoodcutterTaskManager` 正确分配各种任务。
    *   伐木工能否找到目标（树、木材堆、仓库）、移动、执行动作（砍伐、收集、存储）。
    *   `Tree.take_damage()` 和 `WoodSource.collect_wood()` 的正确性。
    *   资源是否正确进入伐木工背包并最终存入仓库（`ResourceManager` 更新）。
    *   树木再生是否按配置工作。
2.  **调试与优化:** (同前)

---

**阶段六：性能优化与扩展性** (内容基本不变，但优化的具体系统会是新的架构)

1. **性能优化措施：** (同前)
2. **扩展性设计：** (同前)
3. **用户界面与反馈**：(同前)

## 补充说明 (内容基本不变，但涉及的系统名会更新)

1. **与农业系统的集成点**：(同前)
2. **项目规范遵循**：本计划将严格遵循项目的编码规范和命名约定，新的专属管理器模式将确保与农业系统架构风格的统一。
3. **未来版本规划**：(同前)
4. **预计挑战与解决方案**：(同前)

Boss，这个修订后的林业系统实现计划，在架构上尽可能地贴近了农业系统的实际实现方式。核心逻辑下放到 `scripts/forestry/` 内的对象和 `scripts/forestry/managers/` 内的专属管理器中。请您审阅！

---
---
## 林业系统完成情况说明 (截至当前，按原计划阶段整理)

**总体状态：核心功能已按计划实现，系统骨架已搭建完成。大部分脚本和管理器已创建并实现了主要逻辑。系统已为初步集成测试做好准备。**

### 阶段一：林业核心框架与基础对象搭建 - 完成情况

1.  **定义 `ForestryTypes.gd`**: **完成**
    *   路径: `scripts/constants/ForestryTypes.gd`
    *   内容与实现说明: (详细说明已包含，如枚举定义)

2.  **设计并创建 `forestry.json`**: **完成**
    *   路径: `config/forestry.json`
    *   内容结构与实现说明: (详细说明已包含，如各配置项)

3.  **扩展 `DataManager.gd`**: **完成**
    *   实现说明: (详细说明已包含，如添加的方法)

4.  **实现 `Tree.gd`**: **完成**
    *   路径: `scripts/forestry/Tree.gd`
    *   职责、主要属性、主要方法、信号、实现说明: (详细说明已包含)

5.  **实现 `WoodSource.gd`**: **完成**
    *   路径: `scripts/forestry/WoodSource.gd`
    *   职责、主要属性、主要方法、信号、实现说明: (详细说明已包含)

6.  **实现 `TreeLifecycleManager.gd`**: **部分移至阶段三说明，基础框架完成**
    *   路径: `scripts/forestry/managers/TreeLifecycleManager.gd`
    *   职责: (骨架搭建，核心引用和配置加载完成)
    *   主要属性: (`_active_trees`, `_tree_growth_progress`, `_stump_timers` 等已定义)
    *   主要方法（基础）: `_ready()`, `_attempt_spawn_new_trees()` (部分实现，依赖GridAreaManager和TileMap), `_on_tree_became_stump()` (部分实现，调用WoodSourceManager), `_parse_collision_rects_from_config()`。
    *   与 `GridAreaManager` 交互: (基础注册/注销逻辑已规划，具体碰撞和类型解析完成)
    *   实现说明: 管理器的基础结构、依赖获取、配置加载已完成。树木生成、砍伐后处理的基础流程已建立。

7.  **实现 `WoodSourceManager.gd`**: **部分移至阶段三说明，基础框架完成**
    *   路径: `scripts/forestry/managers/WoodSourceManager.gd`
    *   职责: (骨架搭建，核心引用和配置加载完成)
    *   主要属性: (`_active_wood_sources` 已定义)
    *   主要方法（基础）: `_ready()`, `create_wood_source_at()` (核心实现), `_on_wood_source_emptied()` (核心实现), `find_nearest_available_wood_source()` (核心实现)。
    *   实现说明: 管理器的基础结构、依赖获取、配置加载已完成。木材源的创建、追踪、移除和查找功能已实现。

8.  **实现 `WoodcutterTaskManager.gd`**: **部分移至阶段四说明，基础框架完成**
    *   路径: `scripts/forestry/managers/WoodcutterTaskManager.gd`
    *   职责: (骨架搭建，核心引用和配置加载完成)
    *   主要属性: (`_registered_woodcutters`, `_task_update_timer` 已定义)
    *   主要方法（基础）: `_ready()`, `register_woodcutter()`, `unregister_woodcutter()`, `_on_woodcutter_task_completed()` (已连接并包含重分配逻辑)。
    *   实现说明: 管理器的基础结构、依赖获取、伐木工注册/注销、任务完成后的回调机制已建立。

9.  **`GameManager.gd` 集成**: **完成**
    *   实现说明: (详细说明已包含，如管理器的实例化和访问器)

### 阶段二：树木与木材源视觉及基础交互实现 - 完成情况

1.  **TileMap 地块配置 (`Tileset.tres`)**: **由用户配置，系统已准备读取**
    *   实现说明: `TreeLifecycleManager` 中的 `is_area_placeable_for_tree` 方法会读取 `forestry.json` 中定义的 `allowed_tile_custom_data`，并查询 `TileMap` 对应图层的自定义数据 (如 `fertile`, `spawn_tree_type`)。

2.  **创建/重构场景 (`Tree.tscn`, `WoodSource.tscn`等)**: **用户负责，脚本已适配**
    *   实现说明: `Tree.gd` 和 `WoodSource.gd` 脚本已包含对 `Sprite2D`, `CollisionShape2D`, `BottomPoint`, `InteractionPointsContainer` 等节点引用的处理逻辑。`Tree.gd` 的 `_update_visuals_and_collision()` 会根据配置更新视觉和碰撞。

3.  **初步生成与状态测试相关功能**: **核心功能已实现**
    *   `TreeLifecycleManager._attempt_spawn_new_trees()`: **已实现**，可生成树木。
    *   树木显示与`BottomPoint`: `Tree.gd` 和 `TreeLifecycleManager.gd` 已包含Y-Sort对齐逻辑。**（Y-Sort逻辑已确认并集成到`TreeLifecycleManager._spawn_tree_at`内部）**
    *   `Tree.take_damage()` 和 `became_stump` 信号: **已实现**。
    *   `TreeLifecycleManager` 接收 `became_stump` 并调用 `WoodSourceManager` 创建 `WoodSource`: **已实现**。
    *   `GridAreaManager` 记录占用: **已实现**，`TreeLifecycleManager` 和 `WoodSourceManager` 在对象创建/移除/状态变化时会更新 `GridAreaManager`。

### 阶段三：树木完整生命周期与木材源收集逻辑实现 - 完成情况

1.  **完善 `TreeLifecycleManager.gd`**: **完成**
    *   `_on_growth_tick()`: **已实现**，驱动树木从树苗到成熟的阶段变化，正确调用 `tree.set_growth_stage()`，并更新 `GridAreaManager` 中的占用（如果配置了不同阶段的不同占用类型和碰撞）。
    *   树桩再生逻辑: 在 `_on_tree_became_stump()` 中，根据配置 (`stump_duration_seconds`, `regrowth_time_seconds`, `can_regrow_as_new_sapling`) 使用 `Timer` 节点来安排树桩的移除和新树苗的生成（通过再次调用 `_attempt_spawn_new_trees` 或特定重生方法）。

2.  **完善 `WoodSource.gd` 和 `WoodSourceManager.gd`**: **完成**
    *   `WoodSource.collect_wood()` 及其 `emptied` 信号: **已实现**。
    *   `WoodSourceManager` 在收到 `emptied` 信号后移除 `WoodSource` 实例 (包括从 `GridAreaManager` 注销): **已实现**。
    *   `WoodSourceManager.find_nearest_available_wood_source()`: **已实现**，包含可选搜索区域。

### 阶段四：伐木工角色与任务系统实现 - 完成情况

1.  **重构/实现 `Woodcutter.gd`**: **完成**
    *   路径、继承、体力移除、主要属性、信号、任务接收方法、核心行为逻辑、动画视觉、初始化: (详细说明已在之前生成，并整合至此)
        *   **初始化 (`initialize`)**: 已更新为接收 `StringName` ID，调用 `super.initialize`，并向 `WoodcutterTaskManager` 注册。
        *   **背包 (`inventory`)**: 继承自 `Character.gd` 的简单字典。
        *   **任务方法 (`assign_task_...`)**: 均已实现，会设置内部状态，并调用基类 `move_to_target()`。
        *   **回调方法 (`_on_reached_..._target`, `_on_action_timer_timeout`)**: 实现了各任务阶段的逻辑。
        *   **信号 (`task_completed`)**: 在 `_finish_current_task` 中发出。

2.  **完善 `WoodcutterTaskManager.gd`**: **完成**
    *   职责、主要属性、AI决策逻辑 (`_decide_next_task_for_woodcutter`)、伐木工生成与注册、`task_completed` 信号处理: (详细说明已在之前生成，并整合至此)
        *   **AI决策**: 实现了“存储 -> 收集 -> 砍伐 -> 闲置/行走”的优先级逻辑。
        *   **任务完成处理**: `_on_woodcutter_task_completed` 会立即为伐木工调用 `_decide_next_task_for_woodcutter()`。

3.  **工具与动画**:
    *   实现说明: `Woodcutter.gd` 中的 `play_animation` 方法用于播放砍伐、收集、存储、行走、空闲动画。实际动画资源和 `AnimatedSprite2D`/`AnimationPlayer` 的配置由用户在场景中完成。

### 其他关键点 - 完成情况 (已整合和确认)
*   **`CharacterManager.gd` & `Character.gd` 初始化**: **已完成**，ID统一为 `StringName`。
*   **Y-Sort与视觉深度**: **已完成**，`TreeLifecycleManager` 在生成树木时正确处理Y-Sort节点和 `BottomPoint` 对齐。
*   **区域管理与占用 (`GridAreaManager.gd`)**: **已完成**，林业对象会根据配置正确注册/注销其在网格中的占用。
*   **TileMap自定义数据**: **已完成**，`TreeLifecycleManager` 会读取并使用。
*   **信号系统连接**:
    *   `Tree.health_changed` -> `TreeLifecycleManager._on_tree_health_changed`: **已连接**。
    *   `Tree.became_stump` -> `TreeLifecycleManager._on_tree_became_stump`: **已连接**。
    *   `WoodSource.wood_amount_changed` -> `WoodSourceManager._on_wood_source_amount_changed`: **已连接**。
    *   `WoodSource.emptied` -> `WoodSourceManager._on_wood_source_emptied`: **已连接**。
    *   `Woodcutter.task_completed` -> `WoodcutterTaskManager._on_woodcutter_task_completed`: **已连接**。
