# 配置驱动架构指导文件

## 🎯 核心原则

**禁止硬编码，一切从配置文件或GameConstants获取**

### 三层架构
```
JSON配置文件 → GameConstants常量类 → 业务代码
```

## 📁 已完成的系统

| 系统 | 配置文件 | 常量类 | 状态 |
|------|----------|--------|------|
| 农业 | agriculture.json | AgricultureConstants | ✅ |
| 渔业 | fishing.json | FishingConstants | ✅ |
| 林业 | trees.json | ForestryConstants | ✅ |
| 烹饪 | cooking.json | CookingConstants | ✅ |
| 矿业 | ores.json | MiningConstants | ✅ |
| 动物 | animals.json | AnimalConstants | ✅ |
| 建筑 | buildings.json | BuildingConstants | ✅ |
| UI | - | UIConstants | ✅ |
| 角色 | - | CharacterConstants | ✅ |
| 世界 | - | WorldConstants | ✅ |

## 🔧 标准模式

### 1. 常量定义
```gdscript
class SystemConstants:
    const BASE_TIME: float = 2.0
    const TIMEOUT: float = 15.0
```

### 2. 配置加载
```gdscript
func _load_items_from_config() -> void:
    var data_manager = _get_data_manager_reference()
    if not data_manager:
        items = DEFAULT_ITEMS  # 回退
        return
    # 从配置文件加载...

func _get_data_manager_reference():
    var game_manager = get_node_or_null("/root/_GameManager")
    if is_instance_valid(game_manager):
        return game_manager.get_data_manager()
    return null
```

### 3. 替换硬编码
```gdscript
# ❌ 错误
const TIMEOUT = 15.0

# ✅ 正确
const TIMEOUT = GameConstants.SystemConstants.TASK_TIMEOUT
```

## ⚠️ 关键注意事项

### 常见错误
1. **常量不能用方法调用**
```gdscript
# ❌ 错误
const ACTION_TIMES = GameConstants.get_times()

# ✅ 正确
const ACTION_TIMES = {
    "action": 2.0,
    "collect": 1.5
}
```

2. **未使用参数要加下划线**
```gdscript
# ❌ 错误
func update_state(unused_param: String):

# ✅ 正确
func update_state(_unused_param: String):
```

3. **必须提供回退机制**
```gdscript
if not data_manager:
    items = DEFAULT_ITEMS  # 必须有默认值
```

4. **区分基础资源和物品类型**
```gdscript
# ✅ 正确：基础资源只有金币
const RESOURCE_TYPES = [0]  # 只有COINS

# ❌ 错误：木材、石头是物品类型，不是基础资源
const RESOURCE_TYPES = [0, 1, 2]  # WOOD, COINS, STONE
```

## ✅ 检查清单

### 新系统开发
1. 在GameConstants中创建SystemConstants类
2. 实现_get_data_manager_reference()方法
3. 添加配置加载方法
4. 替换所有硬编码数字和字符串
5. 提供默认值回退

### 必须遵循
- [ ] 无硬编码数字（除了0, 1, -1）
- [ ] 无硬编码字符串数组
- [ ] 所有超时时间从常量获取
- [ ] 物品类型从配置动态加载
- [ ] 提供智能回退机制

---

**AI助手记住：禁止硬编码，一切从配置文件或GameConstants获取！**

---

**AI助手记住：禁止硬编码，一切从配置文件或GameConstants获取！**
