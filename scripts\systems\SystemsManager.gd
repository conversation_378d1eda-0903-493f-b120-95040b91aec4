# SystemsManager.gd
# 系统管理器 - 统一管理游戏中的各种系统
class_name SystemsManager
extends Node

## 信号定义 ##
signal systems_initialized()
signal system_added(system_name: String)
signal system_removed(system_name: String)

## 系统实例 ##
var attribute_boost_system: AttributeBoostSystem = null
var systems: Dictionary = {}  # system_name -> system_instance

## 生命周期方法 ##
func _ready() -> void:
    name = "SystemsManager"
    add_to_group("systems_manager")
    
    # 延迟初始化，确保其他管理器已经准备好
    call_deferred("_initialize_systems")

func _initialize_systems() -> void:
    """初始化所有系统"""
    print("SystemsManager: 开始初始化系统...")
    
    # 创建属性增强系统
    _create_attribute_boost_system()
    
    # 等待一帧再发送初始化完成信号
    await get_tree().process_frame
    systems_initialized.emit()
    print("SystemsManager: 系统初始化完成")

## 属性增强系统管理 ##
func _create_attribute_boost_system() -> void:
    """创建属性增强系统"""
    if is_instance_valid(attribute_boost_system):
        return
    
    attribute_boost_system = AttributeBoostSystem.new()
    attribute_boost_system.add_to_group("attribute_boost_system")
    add_child(attribute_boost_system)
    
    systems["attribute_boost"] = attribute_boost_system
    system_added.emit("attribute_boost")
    
    print("SystemsManager: AttributeBoostSystem 已创建并初始化")

func get_attribute_boost_system() -> AttributeBoostSystem:
    """获取属性增强系统实例"""
    return attribute_boost_system

## 系统管理 ##
func add_system(system_name: String, system_instance: Node) -> void:
    """添加系统"""
    if systems.has(system_name):
        print("SystemsManager: 警告 - 系统 %s 已存在，将被替换" % system_name)
        remove_system(system_name)
    
    systems[system_name] = system_instance
    if not system_instance.get_parent():
        add_child(system_instance)
    
    system_added.emit(system_name)
    print("SystemsManager: 系统 %s 已添加" % system_name)

func remove_system(system_name: String) -> void:
    """移除系统"""
    if not systems.has(system_name):
        return
    
    var system_instance = systems[system_name]
    if is_instance_valid(system_instance):
        if system_instance.get_parent() == self:
            remove_child(system_instance)
        system_instance.queue_free()
    
    systems.erase(system_name)
    system_removed.emit(system_name)
    print("SystemsManager: 系统 %s 已移除" % system_name)

func get_system(system_name: String) -> Node:
    """获取系统实例"""
    return systems.get(system_name, null)

func has_system(system_name: String) -> bool:
    """检查是否有指定系统"""
    return systems.has(system_name) and is_instance_valid(systems[system_name])

## 调试和状态 ##
func get_systems_status() -> Dictionary:
    """获取所有系统状态"""
    var status = {}
    for system_name in systems:
        var system_instance = systems[system_name]
        status[system_name] = {
            "valid": is_instance_valid(system_instance),
            "class": system_instance.get_class() if is_instance_valid(system_instance) else "Invalid",
            "children_count": system_instance.get_child_count() if is_instance_valid(system_instance) else 0
        }
    return status

func print_systems_status() -> void:
    """打印系统状态（调试用）"""
    print("=== SystemsManager 状态 ===")
    var status = get_systems_status()
    for system_name in status:
        var info = status[system_name]
        print("系统 %s: 有效=%s, 类=%s, 子节点=%d" % [
            system_name, info.valid, info.class, info.children_count
        ])
    print("========================")

## 清理 ##
func cleanup() -> void:
    """清理所有系统"""
    for system_name in systems.keys():
        remove_system(system_name)
    systems.clear()

func _exit_tree() -> void:
    cleanup() 