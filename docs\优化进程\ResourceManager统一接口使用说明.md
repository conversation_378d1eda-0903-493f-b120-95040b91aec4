# ResourceManager 统一接口使用说明

## 📋 概述

ResourceManager 提供了统一的资源操作接口，支持所有类型的资源和物品操作。**所有涉及资源操作的代码都必须使用这些统一接口**，避免直接访问内部存储。

## 🎯 核心统一接口

### 1. 查询接口

```gdscript
# ✅ 正确：使用统一接口
var amount = resource_manager.get_item_amount(item_id)

# ❌ 错误：直接访问内部存储
var amount = resource_manager._building_items.get(item_id, 0)
```

**支持的资源类型：**
- 基础资源：`"coins"`
- 作物种植：`"wheat"`, `"radish"`, `"potato"` 等
- 作物物品：`"wheat_item"`, `"radish_item"`, `"potato_item"` 等
- 建筑物品：`"storage"`, `"farmland"`, `"kitchen"` 等
- 工具物品：`"basic_scythe"`, `"medium_axe"` 等
- 消耗品：`"basic_bucket"`, `"basic_bait"` 等
- 鱼类：`"bluegill"`, `"salmon"`, `"tuna"` 等
- 矿物：`"stone"`, `"iron"`, `"gold"` 等
- 动物产品：`"milk"`, `"egg"`, `"wool"` 等

### 2. 修改接口

```gdscript
# ✅ 添加物品
var added = resource_manager.add_item(item_id, amount)

# ✅ 移除物品
var removed = resource_manager.remove_item(item_id, amount)

# ✅ 消耗物品（等同于remove_item）
var consumed = resource_manager.consume_item(item_id, amount)

# ✅ 直接改变数量（可正可负）
var actual_change = resource_manager.change_item_amount(item_id, change_amount)
```

### 3. 类型查询接口

```gdscript
# ✅ 获取物品类型
var item_type = resource_manager.get_item_type(item_id)

# ✅ 获取指定类型的所有物品
var items_dict = resource_manager.get_items_by_type(ResourceManager.ItemType.BUILDING)
```

## 🔧 特殊系统说明

### 作物系统双重架构

作物系统有两套ID和存储：

```gdscript
# 🌱 作物种植资源（用于农业系统）
resource_manager.get_item_amount("wheat")      # 返回种植用种子数量
resource_manager.add_item("wheat", 10)         # 添加种子

# 🥖 作物收成物品（用于库存和烹饪）
resource_manager.get_item_amount("wheat_item") # 返回收获的小麦数量
resource_manager.add_item("wheat_item", 5)     # 添加收获物品

# 📊 数量关系：作物物品数量 = 种植资源数量 / 2
# 当种植资源有40个时，作物物品显示20个
```

### 信号系统

所有资源变化都会发射统一信号：

```gdscript
# 🔔 监听资源变化
resource_manager.item_changed.connect(_on_item_changed)

func _on_item_changed(item_id: String, change_amount: int, new_amount: int):
    print("物品 %s 变化了 %d，当前数量：%d" % [item_id, change_amount, new_amount])
```

## ❌ 禁止使用的旧接口

以下方法已弃用，**禁止使用**：

```gdscript
# ❌ 禁止：特定类型的旧方法
resource_manager.get_animal_amount()
resource_manager.increase_animal_resource()
resource_manager.decrease_animal_resource()
resource_manager.add_building_item()
resource_manager.consume_building_item()

# ❌ 禁止：直接访问内部存储
resource_manager._building_items[item_id]
resource_manager._tool_items[item_id]
resource_manager._dynamic_crop_resources[item_id]
```

## 📝 最佳实践

### 1. UI显示

```gdscript
# ✅ 正确的库存显示逻辑
func _refresh_inventory():
    for item_type in ResourceManager.ItemType.values():
        var items_dict = resource_manager.get_items_by_type(item_type)
        for item_id in items_dict:
            var amount = resource_manager.get_item_amount(item_id)
            if amount > 0:
                _display_item(item_id, amount)
```

### 2. 交易系统

```gdscript
# ✅ 正确的物品转移逻辑
func transfer_item_to_trading(item_id: String, amount: int) -> bool:
    var available = resource_manager.get_item_amount(item_id)
    if available < amount:
        return false
    
    var removed = resource_manager.remove_item(item_id, amount)
    if removed > 0:
        _add_to_trading_list(item_id, removed)
        return true
    return false
```

### 3. 建筑系统

```gdscript
# ✅ 正确的建筑消耗逻辑
func build_structure(building_id: String) -> bool:
    var cost = get_building_cost(building_id)
    
    # 检查资源是否足够
    for resource_id in cost:
        var required = cost[resource_id]
        var available = resource_manager.get_item_amount(resource_id)
        if available < required:
            return false
    
    # 消耗资源
    for resource_id in cost:
        resource_manager.remove_item(resource_id, cost[resource_id])
    
    return true
```

## 🚨 常见错误

### 错误1：混用新旧接口
```gdscript
# ❌ 错误：查询用新接口，修改用旧方法
var amount = resource_manager.get_item_amount("horse")
resource_manager.increase_animal_resource("horse", 1)  # 旧方法！

# ✅ 正确：统一使用新接口
var amount = resource_manager.get_item_amount("horse")
resource_manager.add_item("horse", 1)
```

### 错误2：作物ID混淆
```gdscript
# ❌ 错误：库存显示种植资源
inventory_ui.display_item("wheat", amount)  # 显示种子图标

# ✅ 正确：库存显示收成物品
inventory_ui.display_item("wheat_item", amount)  # 显示收成图标
```

### 错误3：忽略返回值
```gdscript
# ❌ 错误：不检查操作结果
resource_manager.remove_item("coins", 100)

# ✅ 正确：检查实际操作结果
var removed = resource_manager.remove_item("coins", 100)
if removed < 100:
    show_error("金币不足！")
```

## 🔧 代码优化指南

### 避免冗余包装方法

❌ **错误：创建不必要的包装方法**
```gdscript
func _consume_item_from_inventory(item_id: String, amount: int) -> int:
    return resource_manager.consume_item(item_id, amount)

func _return_item_to_inventory(item_id: String, amount: int) -> int:
    return resource_manager.add_item(item_id, amount)
```

✅ **正确：直接使用统一接口**
```gdscript
# 直接调用，无需包装
var consumed = resource_manager.remove_item(item_id, amount)
var added = resource_manager.add_item(item_id, amount)
```

### 简化调试日志

❌ **错误：过多的调试输出**
```gdscript
print("尝试扣除 %s x%d" % [item_id, amount])
var before = resource_manager.get_item_amount(item_id)
var result = resource_manager.remove_item(item_id, amount)
var after = resource_manager.get_item_amount(item_id)
print("扣除结果 - 之前:%d, 扣除:%d, 之后:%d" % [before, result, after])
```

✅ **正确：简洁的错误处理**
```gdscript
var result = resource_manager.remove_item(item_id, amount)
if result <= 0:
    show_error("操作失败")
    return
```

## 📋 检查清单

在编写涉及资源的代码时，请确认：

- [ ] 使用 `get_item_amount()` 查询数量
- [ ] 使用 `add_item()` / `remove_item()` 修改数量
- [ ] 检查操作返回值
- [ ] 监听 `item_changed` 信号更新UI
- [ ] 区分作物种植资源和作物物品
- [ ] 避免直接访问内部存储
- [ ] 不使用已弃用的旧方法
- [ ] 不创建不必要的包装方法
- [ ] 保持代码简洁，避免过多调试日志

## 🎯 优化完成状态

以下系统已完成统一化优化：

- ✅ **ResourceManager**: 核心统一接口
- ✅ **InventoryUI**: 库存显示系统
- ✅ **TradingpostUI**: 交易站系统
- ✅ **作物系统**: 双重架构统一处理
- ✅ **调试日志**: 清理冗余输出
- ✅ **代码结构**: 移除冗余包装方法

---

**遵循这些规范，确保资源系统的一致性和可维护性！** 🎯
