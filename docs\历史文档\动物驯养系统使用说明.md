# 动物驯养系统使用说明

## 🎯 系统概述

这是一个简化版的动物驯养系统，专为放置类游戏设计，具有以下特点：
- **简单易用**：驯养只需点击3次，无复杂操作
- **自动化程度高**：自动喂食、自动生产、自动繁殖
- **完整生命周期**：从野生→驯养→繁殖→死亡的完整循环

## 🚀 快速开始

### 1. 测试功能
游戏启动后，屏幕顶部会出现测试按钮：
- **生成野生动物**：立即生成一只随机野生动物（只在地上场景生成）
- **动物信息**：显示当前野生动物数量和生成权重
- **清除动物**：清除所有野生动物
- **创建围栏**：创建一个测试用的动物围栏（带UI交互）

### 2. 快捷键
- **F1**：生成野生动物
- **F2**：显示动物信息
- **F3**：清除所有动物
- **F4**：测试驯养系统

### 3. 基本操作流程
1. **创建围栏**：点击"创建围栏"按钮或放置动物围栏建筑
2. **生成动物**：等待自动生成或手动生成野生动物
3. **驯养动物**：点击野生动物3次进行驯养
4. **管理围栏**：点击围栏的UI交互区域打开管理界面
5. **查看状态**：在围栏UI中查看动物状态、繁殖进度、产出等

## 📋 系统功能详解

### 野生动物生成系统
- **自动生成**：每30秒自动生成一只野生动物
- **生成权重**：
  - 鸡：30%（最常见）
  - 鸭：25%
  - 羊：20%
  - 猪：15%
  - 牛：8%
  - 马：2%（最稀有）
- **随机属性**：每只动物都有随机的性别、年龄、寿命

### 驯养系统
1. **驯养方法**：点击野生动物3次即可驯养
2. **驯养进度**：每次点击会显示剩余点击次数
3. **驯养成功**：动物会播放缩放动画，状态变为"驯养"

### 动物属性系统
每只动物都有以下属性：
- **性别**：雄性♂ / 雌性♀
- **年龄**：幼年 / 成年（30秒成长）
- **状态**：野生 / 驯养 / 繁殖中
- **寿命**：基础寿命的80%-120%随机
- **需求**：饱食度、满意度、忠诚度（仅驯养动物）

### 围栏系统
- **容量**：最多容纳10只动物
- **自动喂食**：每60秒自动喂食饥饿的动物
- **自动生产**：成年动物每隔一定时间产出资源
- **自动繁殖**：每30秒检查繁殖机会，同类异性自动配对

### 繁殖系统
- **繁殖条件**：
  - 必须是同一物种
  - 必须是异性
  - 必须都是成年
  - 必须都是驯养状态
- **繁殖时间**：60秒
- **成功率**：80%基础成功率
- **后代**：随机性别的幼年动物

### 需求系统（仅驯养动物）
- **饱食度**：每秒下降0.5，低于30时影响满意度
- **满意度**：每秒下降0.2，受饥饿度影响
- **忠诚度**：每秒下降0.1，受饥饿度和满意度影响
- **野化风险**：忠诚度低于10时有野化风险

### 生命周期系统
- **成长**：幼年动物30秒后成长为成年
- **寿命**：每只动物都有随机寿命，到期自然死亡
- **死亡效果**：淡出动画和"寿终正寝"文字提示

## 🔧 技术实现

### 核心组件
1. **WildAnimalSpawner.gd**：野生动物生成器
2. **AnimalVariant.gd**：动物变体组件（添加到现有动物场景）
3. **AnimalPenBuilding.gd**：动物围栏建筑
4. **SimpleBreedingManager.gd**：简化繁殖管理器
5. **AnimalPenUI.gd**：围栏管理UI

### 配置文件
- **config/animals.json**：动物配置，包含场景路径、驯养难度、寿命等

### 兼容性
- **完全保留**现有动物场景文件
- **无缝集成**现有Animal.gd基类
- **向后兼容**现有动物系统

## 🎮 游戏玩法

### 基础玩法
1. 等待野生动物生成或手动生成
2. 点击野生动物3次进行驯养
3. 建造围栏收容驯养动物
4. 定期喂食和收集产出

### 进阶玩法
1. 收集不同种类的动物
2. 配对繁殖获得更多动物
3. 管理动物需求保持高忠诚度
4. 优化围栏布局提高效率

## 🐛 调试功能

### 测试器功能
- 强制生成动物
- 查看系统状态
- 模拟驯养过程
- 创建测试围栏

### 控制台输出
系统会输出详细的调试信息：
- 动物生成日志
- 驯养过程日志
- 繁殖过程日志
- 需求变化日志

## 📈 后续扩展

### 可扩展功能
1. **品质系统**：普通、优秀、传说品质
2. **特殊能力**：不同品质动物的特殊效果
3. **饲料系统**：不同饲料的不同效果
4. **装饰系统**：围栏装饰影响动物满意度
5. **交易系统**：与其他玩家交易动物

### 平衡调节
所有数值都在配置文件中，可以轻松调节：
- 生成间隔和权重
- 驯养难度
- 繁殖成功率
- 需求下降速率
- 寿命范围

## 🎉 总结

这个简化版动物驯养系统在保持简单易用的同时，提供了完整的动物生命周期体验。系统设计充分考虑了放置类游戏的特点，自动化程度高，玩家操作简单，非常适合作为游戏的核心玩法之一。

通过模块化的设计，系统具有良好的扩展性，可以根据游戏发展需要逐步添加更多功能。
