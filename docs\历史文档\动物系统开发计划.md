# 动物系统开发计划

## 项目概述

基于当前项目的分离式架构设计，新增动物系统以支持在场景中创建不同动物（鸡、牛、鸭、羊、马、猪等），这些动物将按配置时间产出不同资源（鸡蛋、牛奶、肉、皮毛等），供烹饪、制作等系统使用。

## 设计原则

1. **分离式开发**：不影响现有功能，独立测试后再集成
2. **配置驱动**：通过JSON配置文件管理动物属性和行为
3. **简化架构**：动物系统采用简化的管理模式，避免过度设计
4. **玩家参与**：资源掉落后需玩家点击收集，增强互动性

## 整体架构设计

### 1. 核心组件架构

```
动物系统架构
├── 配置层
│   └── config/animals.json          # 动物配置文件
├── 管理层
│   ├── AnimalManager.gd            # 动物管理器
│   └── AnimalSpawner.gd            # 动物生成器
├── 实体层
│   ├── Animal.gd                   # 动物基类
│   ├── animals/
│   │   ├── Chicken.gd              # 鸡
│   │   ├── Cow.gd                  # 牛
│   │   ├── Duck.gd                 # 鸭
│   │   ├── Sheep.gd                # 羊
│   │   ├── Horse.gd                # 马
│   │   └── Pig.gd                  # 猪
│   └── scenes/Animals/             # 动物场景文件
├── 行为层
│   ├── AnimalBehavior.gd           # 动物行为基类
│   ├── AnimalStateMachine.gd       # 状态机
│   └── behaviors/                  # 具体行为实现
└── 资源层
    ├── AnimalResourceDropper.gd    # 资源掉落器
    └── AnimalResourceCollector.gd  # 资源收集器
```

### 2. 与现有系统的集成点

- **ResourceManager**：注册动物产出的新资源类型
- **DataManager**：加载动物配置数据
- **GameManager**：注册AnimalManager
- **UI系统**：动物放置界面（可选，后期功能）

## 详细实现方案

### 阶段一：基础架构搭建（第1-2周）

#### 1.1 配置文件设计

**config/animals.json**
```json
{
  "animal_types": {
    "chicken": {
      "display_name": "鸡",
      "description": "会下蛋的家禽",
      "sprite_config": {
        "atlas_path": "res://assets/animals/chicken_atlas.png",
        "animations": {
          "idle": {"frames": [0, 1, 2, 3], "speed": 2.0},
          "walk": {"frames": [4, 5, 6, 7], "speed": 4.0},
          "eating": {"frames": [8, 9], "speed": 1.0},
          "sleeping": {"frames": [10], "speed": 1.0}
        }
      },
      "base_stats": {
        "health": 100,
        "hunger_max": 100,
        "movement_speed": 50,
        "activity_radius": 100
      },
      "behavior_config": {
        "idle_duration": {"min": 3.0, "max": 8.0},
        "walk_duration": {"min": 2.0, "max": 5.0},
        "eating_duration": {"min": 1.0, "max": 3.0},
        "sleeping_duration": {"min": 5.0, "max": 10.0},
        "state_weights": {
          "idle": 40,
          "walk": 35,
          "eating": 15,
          "sleeping": 10
        }
      },
      "production_config": {
        "resource_type": "egg",
        "production_interval": 300.0,
        "production_amount": {"min": 1, "max": 2},
        "requires_food": false,
        "production_conditions": []
      },
      "scene_path": "res://scenes/Animals/Chicken.tscn"
    },
    "cow": {
      "display_name": "牛",
      "description": "产奶的大型家畜",
      "sprite_config": {
        "atlas_path": "res://assets/animals/cow_atlas.png",
        "animations": {
          "idle": {"frames": [0, 1], "speed": 1.0},
          "walk": {"frames": [2, 3, 4, 5], "speed": 3.0},
          "grazing": {"frames": [6, 7, 8], "speed": 1.5},
          "sleeping": {"frames": [9], "speed": 1.0}
        }
      },
      "base_stats": {
        "health": 200,
        "hunger_max": 150,
        "movement_speed": 30,
        "activity_radius": 80
      },
      "behavior_config": {
        "idle_duration": {"min": 5.0, "max": 12.0},
        "walk_duration": {"min": 3.0, "max": 8.0},
        "grazing_duration": {"min": 4.0, "max": 10.0},
        "sleeping_duration": {"min": 8.0, "max": 15.0},
        "state_weights": {
          "idle": 30,
          "walk": 25,
          "grazing": 30,
          "sleeping": 15
        }
      },
      "production_config": {
        "resource_type": "milk",
        "production_interval": 600.0,
        "production_amount": {"min": 2, "max": 4},
        "requires_food": false,
        "production_conditions": []
      },
      "scene_path": "res://scenes/Animals/Cow.tscn"
    }
  },
  "resource_types": {
    "egg": {
      "display_name": "鸡蛋",
      "description": "新鲜的鸡蛋",
      "icon_region": {"x": 0, "y": 0, "width": 16, "height": 16},
      "stack_size": 50,
      "value": 5
    },
    "milk": {
      "display_name": "牛奶",
      "description": "新鲜的牛奶",
      "icon_region": {"x": 16, "y": 0, "width": 16, "height": 16},
      "stack_size": 20,
      "value": 8
    }
  },
  "global_settings": {
    "max_animals_per_type": 10,
    "animal_despawn_time": 3600.0,
    "resource_drop_cleanup_time": 300.0,
    "enable_hunger_system": false
  }
}
```

#### 1.2 动物基类设计

**scripts/animals/Animal.gd**
```gdscript
# Animal.gd
# 动物基类，提供所有动物的共同功能
class_name Animal
extends Node2D

## 信号定义
signal animal_died(animal: Animal)
signal resource_produced(animal: Animal, resource_type: String, amount: int)
signal state_changed(animal: Animal, old_state: String, new_state: String)

## 枚举定义
enum AnimalState {
    IDLE, WALKING, EATING, SLEEPING, PRODUCING
}

## 基础属性
var animal_id: String = ""
var animal_type: String = ""
var display_name: String = ""
var current_health: int = 100
var max_health: int = 100
var current_hunger: int = 100
var max_hunger: int = 100
var movement_speed: float = 50.0
var activity_radius: float = 100.0

## 状态管理
var current_state: AnimalState = AnimalState.IDLE
var state_timer: float = 0.0
var state_duration: float = 5.0

## 生产相关
var production_timer: float = 0.0
var production_interval: float = 300.0
var last_production_time: float = 0.0

## 组件引用
var animated_sprite: AnimatedSprite2D
var collision_shape: CollisionShape2D
var interaction_area: Area2D
var behavior_manager: AnimalBehavior
var resource_dropper: AnimalResourceDropper

## 配置数据
var config_data: Dictionary = {}

func _ready() -> void:
    _setup_components()
    _setup_interaction()

func initialize(type: String, config: Dictionary) -> void:
    animal_type = type
    config_data = config
    _apply_config()
    _start_behavior()

func _apply_config() -> void:
    # 应用基础属性配置
    # 设置动画
    # 配置行为参数

func _process(delta: float) -> void:
    _update_state(delta)
    _update_production(delta)
    _update_behavior(delta)

# 状态管理方法
# 生产管理方法  
# 行为管理方法
# 交互处理方法
```

#### 1.3 动物管理器设计

**scripts/managers/AnimalManager.gd**
```gdscript
# AnimalManager.gd
# 动物系统管理器
class_name AnimalManager
extends Node

## 信号定义
signal animal_spawned(animal: Animal)
signal animal_removed(animal_id: String)
signal animals_config_loaded()

## 属性
var animals: Dictionary = {}  # animal_id -> Animal
var animal_count_by_type: Dictionary = {}
var animals_config: Dictionary = {}
var is_initialized: bool = false

## 管理器引用
var _game_manager = null
var _resource_manager = null
var _data_manager = null

func _ready() -> void:
    name = "AnimalManager"
    _initialize_manager()

func _initialize_manager() -> void:
    _get_manager_references()
    _load_animals_config()
    _register_resource_types()
    is_initialized = true

func spawn_animal(type: String, position: Vector2) -> Animal:
    # 创建动物实例
    # 设置位置和配置
    # 注册到管理器
    # 返回动物实例

func remove_animal(animal_id: String) -> bool:
    # 从管理器移除
    # 清理资源
    # 发送信号

func get_animals_by_type(type: String) -> Array:
    # 返回指定类型的所有动物

func can_spawn_animal(type: String) -> bool:
    # 检查是否可以生成更多该类型动物

# 配置管理方法
# 资源注册方法
# 清理方法
```

### 阶段二：核心功能实现（第3-4周）

#### 2.1 动物行为系统

**scripts/animals/AnimalBehavior.gd**
```gdscript
# AnimalBehavior.gd
# 动物行为管理器
class_name AnimalBehavior
extends Node

var animal: Animal
var state_machine: AnimalStateMachine
var behavior_config: Dictionary = {}

func initialize(owner_animal: Animal, config: Dictionary) -> void:
    animal = owner_animal
    behavior_config = config
    _setup_state_machine()

func _setup_state_machine() -> void:
    state_machine = AnimalStateMachine.new()
    state_machine.initialize(animal, behavior_config)
    add_child(state_machine)

func update_behavior(delta: float) -> void:
    if state_machine:
        state_machine.update(delta)

# 行为切换逻辑
# 随机行为选择
# 环境感知
```

#### 2.2 资源生产系统

**scripts/animals/AnimalResourceDropper.gd**
```gdscript
# AnimalResourceDropper.gd
# 动物资源掉落管理器
class_name AnimalResourceDropper
extends Node

var animal: Animal
var production_config: Dictionary = {}
var drop_scene = preload("res://scenes/Objects/DroppedItem.tscn")

func initialize(owner_animal: Animal, config: Dictionary) -> void:
    animal = owner_animal
    production_config = config

func try_produce_resource() -> bool:
    if not _can_produce():
        return false
    
    var resource_type = production_config.get("resource_type", "")
    var amount_config = production_config.get("production_amount", {"min": 1, "max": 1})
    var amount = randi_range(amount_config.min, amount_config.max)
    
    _create_dropped_resource(resource_type, amount)
    animal.resource_produced.emit(animal, resource_type, amount)
    return true

func _create_dropped_resource(resource_type: String, amount: int) -> void:
    var dropped_item = drop_scene.instantiate()
    dropped_item.setup_item(resource_type, amount, "animal_production")
    
    # 在动物周围随机位置掉落
    var drop_position = animal.global_position + Vector2(
        randf_range(-20, 20),
        randf_range(-20, 20)
    )
    dropped_item.global_position = drop_position
    
    get_tree().current_scene.add_child(dropped_item)

func _can_produce() -> bool:
    # 检查生产条件
    # 检查时间间隔
    # 检查动物状态
    return true
```

### 阶段三：具体动物实现（第5-6周）

#### 3.1 鸡的实现示例

**scripts/animals/types/Chicken.gd**
```gdscript
# Chicken.gd
# 鸡的具体实现
class_name Chicken
extends Animal

func _ready() -> void:
    super._ready()
    animal_type = "chicken"

func _apply_config() -> void:
    super._apply_config()
    # 鸡特有的配置应用
    
    # 设置鸡特有的动画
    if animated_sprite and config_data.has("sprite_config"):
        _setup_chicken_animations()

func _setup_chicken_animations() -> void:
    var sprite_config = config_data.get("sprite_config", {})
    # 设置鸡的动画帧
    # 配置动画速度

func _update_behavior(delta: float) -> void:
    super._update_behavior(delta)
    # 鸡特有的行为逻辑
    _handle_chicken_behavior(delta)

func _handle_chicken_behavior(delta: float) -> void:
    match current_state:
        AnimalState.IDLE:
            _handle_idle_state(delta)
        AnimalState.WALKING:
            _handle_walking_state(delta)
        AnimalState.EATING:
            _handle_eating_state(delta)
        # 其他状态处理

# 鸡特有的方法实现
```

### 阶段四：UI和交互（第7周）

#### 4.1 动物放置界面（可选）

**scripts/ui/AnimalPlacementUI.gd**
```gdscript
# AnimalPlacementUI.gd
# 动物放置界面
class_name AnimalPlacementUI
extends Control

var animal_manager: AnimalManager
var selected_animal_type: String = ""
var placement_mode: bool = false

func _ready() -> void:
    _setup_animal_buttons()
    _connect_signals()

func _setup_animal_buttons() -> void:
    # 创建动物类型按钮
    # 设置按钮图标和文本

func _on_animal_button_pressed(animal_type: String) -> void:
    selected_animal_type = animal_type
    placement_mode = true
    # 开始放置模式

func _on_placement_confirmed(position: Vector2) -> void:
    if animal_manager and not selected_animal_type.is_empty():
        animal_manager.spawn_animal(selected_animal_type, position)
    placement_mode = false
```

### 阶段五：测试和优化（第8周）

#### 5.1 单元测试

**tests/test_animal_system.gd**
```gdscript
# test_animal_system.gd
# 动物系统测试
extends "res://addons/gut/test.gd"

var animal_manager: AnimalManager
var test_animal: Animal

func before_each():
    animal_manager = AnimalManager.new()
    add_child(animal_manager)

func test_animal_spawn():
    var chicken = animal_manager.spawn_animal("chicken", Vector2(100, 100))
    assert_not_null(chicken)
    assert_eq(chicken.animal_type, "chicken")

func test_resource_production():
    var chicken = animal_manager.spawn_animal("chicken", Vector2(100, 100))
    # 模拟时间经过
    # 检查是否产出资源

func after_each():
    if animal_manager:
        animal_manager.queue_free()
```

## 技术实现细节

### 1. 动物状态机设计

```gdscript
# AnimalStateMachine.gd
class_name AnimalStateMachine
extends Node

enum State { IDLE, WALKING, EATING, SLEEPING, PRODUCING }

var current_state: State = State.IDLE
var state_timer: float = 0.0
var state_durations: Dictionary = {}
var state_weights: Dictionary = {}

func update(delta: float) -> void:
    state_timer += delta
    
    if state_timer >= get_current_state_duration():
        _transition_to_next_state()

func _transition_to_next_state() -> void:
    var next_state = _choose_next_state()
    _change_state(next_state)

func _choose_next_state() -> State:
    # 基于权重随机选择下一个状态
    var total_weight = 0
    for weight in state_weights.values():
        total_weight += weight
    
    var random_value = randf() * total_weight
    var accumulated_weight = 0
    
    for state in state_weights:
        accumulated_weight += state_weights[state]
        if random_value <= accumulated_weight:
            return state
    
    return State.IDLE
```

### 2. 资源掉落优化

```gdscript
# 优化的资源掉落系统
func _create_optimized_dropped_resource(resource_type: String, amount: int) -> void:
    # 检查附近是否有相同类型的掉落物
    var nearby_items = _find_nearby_dropped_items(animal.global_position, 30.0)
    
    for item in nearby_items:
        if item.get_item_type() == resource_type:
            # 合并到现有掉落物
            item.add_amount(amount)
            return
    
    # 创建新的掉落物
    _create_new_dropped_item(resource_type, amount)
```

### 3. 性能优化策略

1. **对象池管理**：复用动物实例，避免频繁创建销毁
2. **LOD系统**：距离玩家较远的动物降低更新频率
3. **批量处理**：将相同类型动物的更新批量处理
4. **空间分区**：使用四叉树等数据结构优化碰撞检测

## 开发时间表

| 阶段 | 时间 | 主要任务 | 交付物 |
|------|------|----------|--------|
| 阶段一 | 第1-2周 | 基础架构搭建 | 配置文件、基类、管理器 |
| 阶段二 | 第3-4周 | 核心功能实现 | 行为系统、生产系统 |
| 阶段三 | 第5-6周 | 具体动物实现 | 6种动物的完整实现 |
| 阶段四 | 第7周 | UI和交互 | 放置界面、交互优化 |
| 阶段五 | 第8周 | 测试和优化 | 测试用例、性能优化 |

## 风险评估和应对

### 主要风险
1. **性能问题**：大量动物同时活动可能影响性能
2. **内存泄漏**：动物对象的生命周期管理
3. **状态同步**：动物状态与UI显示的同步
4. **资源平衡**：动物生产速度对游戏平衡的影响

### 应对策略
1. **性能监控**：实时监控帧率和内存使用
2. **分批更新**：将动物更新分散到多帧
3. **状态缓存**：缓存状态变化，减少不必要的更新
4. **配置调优**：通过配置文件灵活调整生产参数

## 后期扩展功能

### 1. 食槽系统
- 可放置的食槽建筑
- 动物自动寻找食槽进食
- 饥饿系统影响生产效率

### 2. 动物繁殖
- 相同类型动物的繁殖机制
- 幼体成长系统
- 基因系统影响产出品质

### 3. 动物疾病
- 疾病传播机制
- 治疗药物系统
- 隔离和预防措施

### 4. 高级AI行为
- 群体行为模拟
- 环境适应性
- 学习型行为模式

## 总结

本开发计划采用渐进式开发方式，确保每个阶段都有可交付的成果。通过分离式架构设计，动物系统可以独立开发和测试，不会影响现有功能。配置驱动的设计使得后期调整和扩展变得容易，为游戏的长期发展奠定了良好基础。

整个系统设计注重简洁性和可扩展性的平衡，既满足当前需求，又为未来功能扩展预留了空间。通过合理的性能优化策略，确保系统在支持大量动物的同时保持良好的游戏体验。 