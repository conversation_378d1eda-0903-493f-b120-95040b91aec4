# TerrainManager.gd
# 简化版地形管理器 - 用于基本地形查询和交互
# 📁 位置: scripts/core/TerrainManager.gd
class_name TerrainManager
extends Node

## 枚举 ##
# 地形类型枚举
enum TerrainType {
    GRASS = 0,
    DIRT = 1,
    SAND = 2,
    WATER = 3
}

signal terrain_type_at_changed(position, old_type, new_type)

## 常量 ##
# 使用GameConstants中定义的统一网格大小
const DEFAULT_TILE_SIZE = Vector2i(GameConstants.GRID_SIZE_PIXELS, GameConstants.GRID_SIZE_PIXELS)  # 默认瓦片大小

## 属性 ##
var terrain_tilemap = null
var _is_initialized = false

## 生命周期方法 ##
func _ready():
    pass

## 初始化方法 ##
# 初始化地形管理器
# @param tilemap - 地形TileMap引用
# @return 初始化是否成功
func initialize(tilemap):
    if not is_instance_valid(tilemap):
        push_error("TerrainManager: 无效的TileMap引用")
        return false

    terrain_tilemap = tilemap
    _is_initialized = true
    return true

# 获取管理器名称
func get_manager_name():
    return "TerrainManager"

# 检查管理器是否已初始化
# @return 管理器是否已初始化
func is_initialized() -> bool:
    return _is_initialized

## 地形查询方法 ##
# 获取指定位置的地形类型
# @param position - 瓦片位置
# @return 地形类型，-1表示无效
func get_terrain_type_at(position):
    if not _is_initialized:
        push_error("TerrainManager: 在初始化前尝试获取地形类型")
        return -1

    # 检查位置是否在地图范围内
    if not terrain_tilemap.get_used_rect().has_point(position):
        return -1
    
    # 获取瓦片数据
    var data = terrain_tilemap.get_cell_tile_data(0, position)
    if data:
        # 尝试从自定义数据获取地形类型
        if data.get_custom_data("terrain_type") != null:
            return data.get_custom_data("terrain_type")
    
    # 若无自定义数据，从坐标推断地形类型
    return _infer_terrain_type_from_coords(position)

# 从坐标推断地形类型
# @private
# @param position - 瓦片位置
# @return 推断的地形类型
func _infer_terrain_type_from_coords(position):
    # 简单的推断逻辑，可根据实际需要调整
    # 这里假设：
    # - 边缘区域为水域
    # - 中心区域为草地
    # - 其他为泥土
    
    var map_rect = terrain_tilemap.get_used_rect()
    var center = map_rect.get_center()
    var distance_to_center = position.distance_to(center)
    
    # 距离中心很远的为水域
    if distance_to_center > map_rect.size.x * 0.4:
        return TerrainType.WATER
    # 距离中心较近的为草地
    elif distance_to_center < map_rect.size.x * 0.2:
        return TerrainType.GRASS
    # 其他为泥土
    else:
        return TerrainType.DIRT

# 检查地形是否适合建筑
# @param position - 瓦片位置
# @return 是否可建筑
func is_buildable_terrain(position):
    if not _is_initialized:
        return false
        
    # 从自定义数据获取建筑规则
    var tile_data = terrain_tilemap.get_cell_tile_data(0, position)
    if tile_data:
        var tile_set = terrain_tilemap.tile_set
        if tile_set:
            var placement_layer_id = _find_custom_data_layer_id(tile_set, "placement_rules")
            
            if placement_layer_id >= 0:
                var can_place = tile_data.get_custom_data_by_layer_id(placement_layer_id)
                if can_place != null and can_place is bool:
                    return can_place
    
    # 根据地形类型判断是否可建筑
    var terrain_type = get_terrain_type_at(position)
    
    # 只有草地和泥土可建造
    return terrain_type == TerrainType.GRASS or terrain_type == TerrainType.DIRT

# 查找自定义数据层ID
# @private
# @param tile_set - TileSet引用
# @param layer_name - 层名称
# @return 层ID，-1表示未找到
func _find_custom_data_layer_id(tile_set, layer_name: String) -> int:
    var layers_count = tile_set.get_custom_data_layers_count()
    for i in range(layers_count):
        if tile_set.get_custom_data_layer_name(i) == layer_name:
            return i
    return -1

# 检查地形是否可行走
# @param position - 瓦片位置
# @return 是否可行走
func is_walkable_terrain(position):
    var terrain_type = get_terrain_type_at(position)
    # 水域和无效地形不可行走
    return terrain_type != TerrainType.WATER and terrain_type != -1

# 获取世界边界
# @return 世界边界矩形
func get_world_bounds() -> Rect2i:
    if not _is_initialized or not terrain_tilemap:
        return Rect2i(0, 0, 0, 0)

    return terrain_tilemap.get_used_rect()

# 获取瓦片实际尺寸
# @return 瓦片尺寸
func get_tile_size():
    if not _is_initialized or not terrain_tilemap.tile_set:
        return DEFAULT_TILE_SIZE

    return terrain_tilemap.tile_set.tile_size

# 世界坐标转换为地图坐标
# @param world_position - 世界坐标
# @return 地图坐标
func world_to_map(world_position):
    if not _is_initialized:
        return Vector2i.ZERO
    
    return terrain_tilemap.local_to_map(world_position)

# 地图坐标转换为世界坐标（居中）
# @param map_position - 地图坐标
# @return 世界坐标（瓦片中心）
func map_to_world_centered(map_position):
    if not _is_initialized:
        return Vector2.ZERO
    
    return terrain_tilemap.map_to_local(map_position)

# 获取地形类型名称
# @param terrain_type - 地形类型枚举值
# @return 地形类型名称
func get_terrain_type_name(terrain_type) -> String:
    match terrain_type:
        TerrainType.GRASS: return "草地"
        TerrainType.DIRT: return "泥土"
        TerrainType.SAND: return "沙地"
        TerrainType.WATER: return "水域"
        _: return "未知"

# 获取调试信息
# @return 调试信息字典
func get_debug_info() -> Dictionary:
    return {
        "is_initialized": _is_initialized,
        "has_tilemap": is_instance_valid(terrain_tilemap),
        "world_bounds": get_world_bounds() if _is_initialized else Rect2i(),
        "tile_size": get_tile_size()
    }

# 设置地形类型（如果支持动态修改）
# @param position - 瓦片位置
# @param new_type - 新的地形类型
# @return 是否设置成功
func set_terrain_type_at(position, new_type) -> bool:
    if not _is_initialized:
        return false
    
    var old_type = get_terrain_type_at(position)
    if old_type == new_type:
        return true
    
    # 这里可以实现实际的地形修改逻辑
    # 目前只发送信号通知变化
    terrain_type_at_changed.emit(position, old_type, new_type)
    return true

# 批量检查地形类型
# @param positions - 位置数组
# @return 地形类型数组
func get_terrain_types_at(positions: Array) -> Array:
    var types = []
    for pos in positions:
        types.append(get_terrain_type_at(pos))
    return types

# 查找指定类型的地形
# @param terrain_type - 要查找的地形类型
# @param search_area - 搜索区域（可选）
# @return 符合条件的位置数组
func find_terrain_of_type(terrain_type, search_area: Rect2i = Rect2i()) -> Array:
    if not _is_initialized:
        return []
    
    var found_positions = []
    var search_rect = search_area if search_area.size != Vector2i.ZERO else terrain_tilemap.get_used_rect()
    
    for y in range(search_rect.position.y, search_rect.position.y + search_rect.size.y):
        for x in range(search_rect.position.x, search_rect.position.x + search_rect.size.x):
            var pos = Vector2i(x, y)
            if get_terrain_type_at(pos) == terrain_type:
                found_positions.append(pos)
    
    return found_positions
