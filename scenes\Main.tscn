[gd_scene load_steps=5 format=3 uid="uid://6ewkqxaxikx6"]

[ext_resource type="Script" path="res://scripts/Main.gd" id="1_a1gxd"]
[ext_resource type="Script" path="res://scripts/test/CowVariantTester.gd" id="2_cow_tester"]
[ext_resource type="Texture2D" uid="uid://bgoblbenfxuq8" path="res://assets/animals/Icons.png" id="2_wmf85"]

[sub_resource type="AtlasTexture" id="AtlasTexture_mlk04"]
atlas = ExtResource("2_wmf85")
region = Rect2(64, 64, 16, 16)

[node name="Main" type="Node2D"]
script = ExtResource("1_a1gxd")

[node name="Character" type="Sprite2D" parent="."]

[node name="DebugUI" type="CanvasLayer" parent="."]

[node name="Sprite2D" type="Sprite2D" parent="."]
visible = false
texture = SubResource("AtlasTexture_mlk04")

[node name="CowVariantTester" type="Node" parent="."]
script = ExtResource("2_cow_tester")
