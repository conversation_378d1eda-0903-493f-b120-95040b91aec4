# TestRunner.gd
# 测试运行器 - 统一管理所有测试
extends Node

# 测试脚本引用
const CharacterSystemTest = preload("res://scripts/test/CharacterSystemTest.gd")

# UI控制
@onready var test_output: RichTextLabel
@onready var run_button: Button

var test_results: Array[Dictionary] = []

func _ready():
    setup_ui()
    print("=== 测试运行器已启动 ===")
    print("使用方法：")
    print("1. 点击界面上的'运行所有测试'按钮")
    print("2. 或者调用 run_all_tests() 方法")
    print("3. 或者按F5键快速运行")

func _input(event):
    # F5快速运行测试
    if event.is_action_pressed("ui_accept") or (event is InputEventKey and event.keycode == KEY_F5):
        run_all_tests()

func setup_ui():
    """设置简单的UI界面"""
    # 创建UI容器
    var vbox = VBoxContainer.new()
    add_child(vbox)
    
    # 标题
    var title = Label.new()
    title.text = "角色系统统一化测试运行器"
    title.add_theme_font_size_override("font_size", 20)
    vbox.add_child(title)
    
    # 运行按钮
    run_button = Button.new()
    run_button.text = "运行所有测试 (F5)"
    run_button.pressed.connect(run_all_tests)
    vbox.add_child(run_button)
    
    # 输出区域
    test_output = RichTextLabel.new()
    test_output.custom_minimum_size = Vector2(800, 600)
    test_output.bbcode_enabled = true
    vbox.add_child(test_output)
    
    # 清除按钮
    var clear_button = Button.new()
    clear_button.text = "清除输出"
    clear_button.pressed.connect(clear_output)
    vbox.add_child(clear_button)

func run_all_tests():
    """运行所有测试"""
    clear_output()
    test_results.clear()
    
    add_output("[color=blue][b]=== 开始运行角色系统统一测试 ===[/b][/color]\n")
    add_output("测试时间: %s\n\n" % Time.get_datetime_string_from_system())

    var start_time = Time.get_unix_time_from_system()

    # 运行角色系统统一测试
    add_output("[color=yellow][b]角色系统统一测试[/b][/color]\n")
    var character_result = await run_character_system_test()
    test_results.append(character_result)

    var end_time = Time.get_unix_time_from_system()
    var duration = end_time - start_time

    # 生成测试报告
    generate_test_report(duration)

func run_character_system_test() -> Dictionary:
    """运行角色系统统一测试"""
    var result = {"name": "角色系统统一测试", "passed": false, "details": []}

    # 创建测试实例
    var test_instance = CharacterSystemTest.new()
    if not test_instance:
        result.details.append("无法创建测试实例")
        add_output("[color=red]❌ 角色系统测试失败[/color]\n")
        return result

    add_child(test_instance)

    # 运行测试
    await test_instance.run_all_tests()

    # 清理
    test_instance.queue_free()

    result.passed = true
    add_output("[color=green]✅ 角色系统测试通过[/color]\n")

    return result

# === 工具方法 ===

func generate_test_report(duration: float):
    """生成测试报告"""
    add_output("\n[color=blue][b]=== 测试报告 ===[/b][/color]\n")
    
    var total_tests = test_results.size()
    var passed_tests = 0
    
    for result in test_results:
        if result.passed:
            passed_tests += 1
            add_output("[color=green]✅ %s: 通过[/color]\n" % result.name)
        else:
            add_output("[color=red]❌ %s: 失败[/color]\n" % result.name)
            for detail in result.details:
                add_output("   - %s\n" % detail)
    
    add_output("\n[b]总结:[/b]\n")
    add_output("总测试数: %d\n" % total_tests)
    add_output("通过测试: %d\n" % passed_tests)
    add_output("失败测试: %d\n" % (total_tests - passed_tests))
    add_output("成功率: %.1f%%\n" % (float(passed_tests) / float(total_tests) * 100.0))
    add_output("执行时间: %.2f秒\n" % duration)
    
    if passed_tests == total_tests:
        add_output("\n[color=green][b]🎉 所有测试通过！系统状态良好！[/b][/color]\n")
    else:
        add_output("\n[color=red][b]⚠️ 部分测试失败，需要检查问题！[/b][/color]\n")

func add_output(text: String):
    """添加输出文本"""
    if test_output:
        test_output.append_text(text)
        test_output.scroll_to_line(test_output.get_line_count())
    print(text.strip_edges())

func clear_output():
    """清除输出"""
    if test_output:
        test_output.clear()

# 静态方法供外部调用
static func run_quick_tests():
    """快速运行测试的静态方法"""
    var runner = load("res://scripts/test/TestRunner.gd").new()
    runner.name = "QuickTestRunner"

    # 添加到当前场景
    var current_scene = Engine.get_main_loop().current_scene
    if current_scene:
        current_scene.add_child(runner)
        runner.run_all_tests()
    else:
        print("错误：无法获取当前场景")

# 便捷的全局函数
func _notification(what):
    if what == NOTIFICATION_WM_CLOSE_REQUEST:
        print("测试运行器关闭")
        get_tree().quit()
