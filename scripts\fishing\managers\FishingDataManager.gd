# FishingDataManager.gd
# 渔业数据管理器 - 配置驱动的渔业系统核心
# 📁 位置: scripts/fishing/managers/FishingDataManager.gd
class_name FishingDataManager
extends Node

## 信号定义 ##
signal fishing_data_loaded
signal fishing_data_load_failed(error_message: String)
signal global_fishing_config_changed(rod_id: String, bait_id: String)

## 配置驱动常量 ##
const CONFIG_PATH: String = "res://config/fishing.json"
const DEFAULT_EQUIPMENT_DURABILITY = GameConstants.FishingConstants.DEFAULT_EQUIPMENT_DURABILITY

## 配置数据缓存 ##
var fishing_config: Dictionary = {}
var fishing_rods: Dictionary = {}
var baits: Dictionary = {}
var catch_items: Dictionary = {}
var water_types: Dictionary = {}
var catch_probabilities: Dictionary = {}

## 全局渔业配置管理 ##
var global_fishing_config: Dictionary = {
    "rod_id": "",
    "bait_id": "",
    "is_valid": false
}

## 依赖管理器 ##
var data_manager = null

## 状态标志 ##
var is_loaded: bool = false

## 初始化和加载 ##
func _ready() -> void:
    """初始化渔业数据管理器"""
    _initialize_dependencies()

func _initialize_dependencies() -> void:
    """初始化依赖管理器 - 标准模式"""
    var game_manager = _get_game_manager_reference()
    if game_manager and game_manager.has_method("get_data_manager"):
        data_manager = game_manager.get_data_manager()

func _get_game_manager_reference():
    """获取GameManager引用 - 标准模式"""
    var game_manager = get_node_or_null("/root/_GameManager")
    return game_manager if is_instance_valid(game_manager) else null

func load_fishing_data() -> bool:
    """加载渔业配置数据 - 配置驱动模式"""
    # 尝试从DataManager获取配置
    if is_instance_valid(data_manager) and data_manager.is_initialized:
        var config = data_manager.get_fishing_config()
        if not config.is_empty():
            fishing_config = config
            _cache_configuration_sections()
            is_loaded = true
            fishing_data_loaded.emit()
            return true

    # 回退：直接加载配置文件
    return _load_config_from_file()

func _load_config_from_file() -> bool:
    """从文件加载配置 - 回退机制"""
    if not FileAccess.file_exists(CONFIG_PATH):
        var error_msg = "渔业配置文件不存在: " + str(CONFIG_PATH)
        fishing_data_load_failed.emit(error_msg)
        return false

    var file = FileAccess.open(CONFIG_PATH, FileAccess.READ)
    if not file:
        var error_msg = "无法打开渔业配置文件: " + str(CONFIG_PATH)
        fishing_data_load_failed.emit(error_msg)
        return false

    var json_text = file.get_as_text()
    file.close()

    var json = JSON.new()
    var parse_result = json.parse(json_text)

    if parse_result != OK:
        var error_msg = "解析渔业配置文件失败，错误位置: " + str(json.error_line)
        fishing_data_load_failed.emit(error_msg)
        return false

    fishing_config = json.data
    _cache_configuration_sections()
    is_loaded = true
    fishing_data_loaded.emit()
    return true

func _cache_configuration_sections() -> void:
    """缓存配置文件的各个部分 - 配置驱动模式"""
    # 处理引用结构
    var rod_refs = fishing_config.get("fishing_rod_references", {})
    var bait_refs = fishing_config.get("bait_references", {})

    # 从equipment配置获取渔具数据
    if rod_refs.has("equipment_config_source"):
        fishing_rods = _load_equipment_items("fishing_rods")
    else:
        fishing_rods = fishing_config.get("fishing_rods", {})

    if bait_refs.has("equipment_config_source"):
        baits = _load_equipment_items("baits")
    else:
        baits = fishing_config.get("baits", {})

    # 缓存其他配置
    catch_items = fishing_config.get("catch_items", {})
    water_types = fishing_config.get("water_types", {})
    catch_probabilities = fishing_config.get("catch_probabilities", {})

func _load_equipment_items(section_name: String) -> Dictionary:
    """从装备配置加载渔具数据 - 配置驱动模式"""
    # 优先从DataManager获取
    if is_instance_valid(data_manager) and data_manager.is_initialized:
        var equipment_config = data_manager.get_equipment_config()
        if not equipment_config.is_empty():
            return _filter_fisherman_equipment(equipment_config, section_name)

    # 回退：直接加载文件
    return _load_equipment_from_file(section_name)

func _filter_fisherman_equipment(equipment_config: Dictionary, section_name: String) -> Dictionary:
    """过滤渔夫装备"""
    var equipment_items = equipment_config.get("equipment_items", {})
    var filtered_items = {}

    for item_id in equipment_items:
        var item_data = equipment_items[item_id]
        var compatible_characters = item_data.get("compatible_characters", [])
        var category = item_data.get("category", "")

        if compatible_characters.has("fisherman"):
            if (section_name == "fishing_rods" and category == "main_hand") or \
               (section_name == "baits" and category == "off_hand"):
                filtered_items[item_id] = item_data

    return filtered_items

func _load_equipment_from_file(section_name: String) -> Dictionary:
    """从文件加载装备配置 - 回退机制"""
    var equipment_path = GameConstants.EquipmentConstants.EQUIPMENT_CONFIG_PATH
    if not FileAccess.file_exists(equipment_path):
        push_warning("FishingDataManager: 装备配置文件不存在: " + equipment_path)
        return {}

    var file = FileAccess.open(equipment_path, FileAccess.READ)
    if not file:
        push_error("FishingDataManager: 无法打开装备配置文件")
        return {}

    var json_text = file.get_as_text()
    file.close()

    var json = JSON.new()
    var parse_result = json.parse(json_text)

    if parse_result != OK:
        push_error("FishingDataManager: 解析装备配置失败，行: %d" % json.error_line)
        return {}

    return _filter_fisherman_equipment(json.data, section_name)

## 鱼竿相关方法 ##
func get_fishing_rod_data(rod_id: String) -> Dictionary:
    """获取指定鱼竿的完整数据"""
    return fishing_rods.get(rod_id, {})

func get_all_fishing_rods() -> Dictionary:
    """获取所有鱼竿数据"""
    return fishing_rods

func get_fishing_rod_ids() -> Array:
    """获取所有鱼竿ID列表"""
    return fishing_rods.keys()

func is_fishing_rod_valid(rod_id: String) -> bool:
    """检查鱼竿ID是否有效"""
    return fishing_rods.has(rod_id)

## 鱼饵相关方法 ##
func get_bait_data(bait_id: String) -> Dictionary:
    """获取指定鱼饵的完整数据"""
    return baits.get(bait_id, {})

func get_all_baits() -> Dictionary:
    """获取所有鱼饵数据"""
    return baits

func get_bait_ids() -> Array:
    """获取所有鱼饵ID列表"""
    return baits.keys()

func is_bait_valid(bait_id: String) -> bool:
    """检查鱼饵ID是否有效"""
    return baits.has(bait_id)

## 水体类型相关方法 ##
func get_water_type_data(water_type: String) -> Dictionary:
    """获取指定水体类型数据"""
    return water_types.get(water_type, {})

func get_all_water_types() -> Dictionary:
    """获取所有水体类型数据"""
    return water_types

func is_water_type_valid(water_type: String) -> bool:
    """检查水体类型是否有效"""
    return water_types.has(water_type)

func get_item_data(item_id: String) -> Dictionary:
    """获取任意渔业相关物品的数据（鱼竿、鱼饵、渔获）"""
    if not is_loaded:
        return {}

    # 检查鱼竿
    if fishing_rods.has(item_id):
        return fishing_rods[item_id]

    # 检查鱼饵
    if baits.has(item_id):
        return baits[item_id]

    # 检查渔获
    for category in catch_items:
        if catch_items[category].has(item_id):
            return catch_items[category][item_id]
            
    return {}

## 钓获物相关方法 ##
func get_catch_items_by_category(category: String) -> Dictionary:
    """获取指定类别的钓获物"""
    return catch_items.get(category, {})

func get_catch_items_by_tier(tier: String, water_type: String = "") -> Array:
    """获取指定等级的钓获物"""
    var tier_items = []

    for category in catch_items:
        var category_items = catch_items[category]
        for item_id in category_items:
            var item_data = category_items[item_id]

            # 检查等级匹配
            if str(item_data.get("tier", "")) == tier:
                # 如果指定了水体类型，检查是否匹配
                var should_include = false
                if water_type.is_empty():
                    should_include = true
                else:
                    var item_water_types = item_data.get("water_types", [])
                    should_include = water_type in item_water_types

                if should_include:
                    # 返回包含完整信息的对象
                    tier_items.append({
                        "id": item_id,
                        "category": category,
                        "data": item_data
                    })

    return tier_items

func get_all_catch_item_ids() -> Array:
    """获取所有钓获物ID"""
    var all_ids = []
    for category in catch_items:
        all_ids.append_array(catch_items[category].keys())
    return all_ids

func get_catch_items_by_water_type(water_type: String) -> Array:
    """获取指定水体类型的钓获物"""
    var water_items = []

    for category in catch_items:
        var category_items = catch_items[category]
        for item_id in category_items:
            var item_data = category_items[item_id]
            var item_water_types = item_data.get("water_types", [])

            if water_type in item_water_types:
                water_items.append({
                    "id": item_id,
                    "category": category,
                    "data": item_data
                })

    return water_items

## 概率计算相关方法 ##
func calculate_catch_probability(rod_id: String, bait_id: String, item_id: String, water_type: String) -> float:
    """计算钓获特定物品的概率"""
    if not is_loaded:
        return 0.0
    
    # 获取基础概率配置
    var base_probabilities = catch_probabilities.get("base_probabilities", {})
    var rod_modifiers = catch_probabilities.get("rod_modifiers", {})
    var bait_modifiers = catch_probabilities.get("bait_modifiers", {})
    var water_modifiers = catch_probabilities.get("water_type_modifiers", {})
    
    # 获取物品数据
    var item_data = get_item_data(item_id)
    if item_data.is_empty():
        return 0.0
    
    # 获取物品等级
    var item_tier = item_data.get("tier", "common")
    var base_prob = base_probabilities.get(item_tier, 0.1)
    
    # 应用鱼竿修正
    var rod_modifier = rod_modifiers.get(rod_id, {}).get(item_tier, 1.0)
    
    # 应用鱼饵修正
    var bait_modifier = bait_modifiers.get(bait_id, {}).get(item_tier, 1.0)
    
    # 应用水体类型修正
    var water_modifier = water_modifiers.get(water_type, {}).get(item_tier, 1.0)
    
    # 计算最终概率
    var final_probability = base_prob * rod_modifier * bait_modifier * water_modifier
    
    # 确保概率在合理范围内
    return clamp(final_probability, 0.0, 1.0)

## 概率计算高级方法 ##
func calculate_fishing_probabilities(rod_id: String, bait_id: String, water_type: String) -> Dictionary:
    """计算完整的钓鱼概率分布"""
    var result = {
        "is_valid": false,
        "tier_probabilities": {},
        "item_probabilities": {},
        "total_fish_probability": 0.0,
        "total_junk_probability": 0.0
    }

    if not is_loaded:
        return result

    var rod_data = get_fishing_rod_data(rod_id)
    var bait_data = get_bait_data(bait_id)

    if rod_data.is_empty() or bait_data.is_empty():
        return result

    result.is_valid = true

    # 从鱼饵effects字段获取accessible_fish_tiers
    var effects = bait_data.get("effects", {})
    var accessible_tiers = effects.get("accessible_fish_tiers", [])

    # 如果effects中没有，尝试从根级别获取（兼容旧格式）
    if accessible_tiers.is_empty():
        accessible_tiers = bait_data.get("accessible_fish_tiers", [])

    # 定义基础概率（系统默认值）
    var base_tier_probabilities = {
        "0": 0.3,    # 杂物基础概率
        "1": 0.4,    # 1级鱼基础概率
        "2": 0.2,    # 2级鱼基础概率
        "3": 0.1     # 3级鱼基础概率
    }

    # 获取装备效果数据
    var rod_effects = rod_data.get("effects", {})
    var bait_effects = bait_data.get("effects", {})
    var rod_modifiers = rod_effects.get("probability_modifiers", {})
    var bait_modifiers = bait_effects.get("probability_modifiers", {})

    # 计算各等级概率（包括杂物和鱼类）
    for tier in accessible_tiers:
        # 获取基础概率
        var base_prob = base_tier_probabilities.get(str(tier), 0.0)

        if base_prob > 0.0:
            # 确定修正值键名
            var modifier_key = "junk" if tier == 0 else "tier_%d_fish" % tier
            var rod_modifier = rod_modifiers.get(modifier_key, 1.0)
            var bait_modifier = bait_modifiers.get(modifier_key, 1.0)

            # 计算最终概率
            var final_prob = base_prob * rod_modifier * bait_modifier
            result.tier_probabilities[tier] = final_prob

            # 分类累计概率
            if tier == 0:  # 杂物
                result.total_junk_probability = final_prob
            else:  # 鱼类
                result.total_fish_probability += final_prob

    # 计算具体物品概率
    _calculate_item_probabilities(result, water_type, accessible_tiers)

    # 验证概率计算结果
    _validate_probability_result(result)

    return result

func _calculate_item_probabilities(result: Dictionary, water_type: String, accessible_tiers: Array) -> void:
    """计算具体物品概率分布"""
    result.item_probabilities = {}

    for tier in accessible_tiers:
        var tier_items = get_catch_items_by_tier(str(tier), water_type)
        var tier_prob = result.tier_probabilities.get(tier, 0.0)

        if tier_items.size() > 0 and tier_prob > 0:
            var total_weight = 0.0
            for item_info in tier_items:
                total_weight += item_info.data.get("base_probability", 1.0)

            # 避免除零错误
            if total_weight > 0.0:
                for item_info in tier_items:
                    var item_weight = item_info.data.get("base_probability", 1.0)
                    var item_prob = tier_prob * (item_weight / total_weight)

                    var full_item_id = item_info.category + "." + item_info.id
                    result.item_probabilities[full_item_id] = item_prob

func _validate_probability_result(result: Dictionary) -> void:
    """验证概率计算结果的合理性"""
    if not result.get("is_valid", false):
        return

    var total_prob = result.get("total_fish_probability", 0.0) + result.get("total_junk_probability", 0.0)

    # 警告：总概率异常
    if total_prob <= 0.0:
        push_warning("FishingDataManager: 计算出的总概率为0，可能存在配置问题")
    elif total_prob > 2.0:  # 允许一定的概率超出，但不应过高
        push_warning("FishingDataManager: 计算出的总概率过高 (%.2f)，请检查装备修正值" % total_prob)

    # 验证物品概率总和
    var item_prob_sum = 0.0
    for prob in result.get("item_probabilities", {}).values():
        item_prob_sum += prob

    if abs(item_prob_sum - total_prob) > 0.01:  # 允许小的浮点误差
        push_warning("FishingDataManager: 物品概率总和与等级概率总和不匹配")

## 可制作物品相关方法 ##
func get_craftable_items() -> Array:
    var craftable = []

    for rod_id in fishing_rods.keys():
        var rod_data = fishing_rods[rod_id]
        if rod_data.has("crafting_cost"):
            craftable.append({
                "type": "fishing_rod",
                "id": rod_id,
                "data": rod_data
            })

    for bait_id in baits.keys():
        var bait_data = baits[bait_id]
        if bait_data.has("crafting_cost"):
            craftable.append({
                "type": "bait",
                "id": bait_id,
                "data": bait_data
            })

    return craftable

func get_crafting_cost(item_type: String, item_id: String) -> Dictionary:
    """获取指定物品的制作成本"""
    var item_data = {}

    match item_type:
        "fishing_rod":
            item_data = get_fishing_rod_data(item_id)
        "bait":
            item_data = get_bait_data(item_id)
        _:
            return {}

    return item_data.get("crafting_cost", {})

## 可用钓获物过滤方法 ##
func get_available_catches(_rod_id: String, bait_id: String, water_type: String) -> Array:
    """获取给定配置下可能钓到的所有物品"""
    var bait_data = get_bait_data(bait_id)
    if bait_data.is_empty():
        return []

    # 🆕 从effects字段中获取accessible_fish_tiers
    var effects = bait_data.get("effects", {})
    var accessible_tiers = effects.get("accessible_fish_tiers", [])

    # 🆕 如果effects中没有，尝试从根级别获取（兼容旧格式）
    if accessible_tiers.is_empty():
        accessible_tiers = bait_data.get("accessible_fish_tiers", [])

    var available_catches = []

    for tier in accessible_tiers:
        var tier_items = get_catch_items_by_tier(str(tier), water_type)
        available_catches.append_array(tier_items)

    return available_catches

## 验证和检查方法 ##
func validate_fishing_setup(rod_id: String, bait_id: String, water_type: String) -> Dictionary:
    """验证钓鱼配置的有效性"""
    var validation = {
        "is_valid": true,
        "errors": [],
        "warnings": []
    }

    if not is_fishing_rod_valid(rod_id):
        validation.is_valid = false
        validation.errors.append("无效的鱼竿ID: " + rod_id)

    if not is_bait_valid(bait_id):
        validation.is_valid = false
        validation.errors.append("无效的鱼饵ID: " + bait_id)

    if not is_water_type_valid(water_type):
        validation.is_valid = false
        validation.errors.append("无效的水体类型: " + water_type)

    if validation.is_valid:
        var available_catches = get_available_catches(rod_id, bait_id, water_type)
        if available_catches.is_empty():
            validation.warnings.append("当前配置下没有可钓获的物品")

    return validation

## 工具方法 ##
func get_tier_display_name(tier: int) -> String:
    """获取等级的显示名称 - 从配置文件获取或使用默认值"""
    # 🆕 可以考虑将这些显示名称移到配置文件中
    var tier_names = {
        0: "杂物",
        1: "低等鱼",
        2: "中等鱼",
        3: "高等鱼"
    }
    return tier_names.get(tier, "未知等级")

## 渔夫装备特殊性验证方法 ##
func validate_fisherman_equipment_system() -> Dictionary:
    """验证渔夫装备系统的特殊性处理是否正确"""
    var validation_result = {
        "is_valid": true,
        "errors": [],
        "warnings": [],
        "equipment_combinations_tested": 0,
        "probability_calculations_verified": 0
    }

    if not is_loaded:
        validation_result.is_valid = false
        validation_result.errors.append("FishingDataManager未加载")
        return validation_result

    # 验证装备配置完整性
    var rod_ids = get_fishing_rod_ids()
    var bait_ids = get_bait_ids()

    if rod_ids.is_empty():
        validation_result.errors.append("没有找到任何鱼竿配置")
        validation_result.is_valid = false

    if bait_ids.is_empty():
        validation_result.errors.append("没有找到任何鱼饵配置")
        validation_result.is_valid = false

    # 验证装备组合效果
    for rod_id in rod_ids:
        for bait_id in bait_ids:
            var rod_data = get_fishing_rod_data(rod_id)
            var bait_data = get_bait_data(bait_id)

            # 检查effects字段
            if not rod_data.has("effects"):
                validation_result.warnings.append("鱼竿 %s 缺少effects字段" % rod_id)
            if not bait_data.has("effects"):
                validation_result.warnings.append("鱼饵 %s 缺少effects字段" % bait_id)

            # 检查概率修正值
            var rod_effects = rod_data.get("effects", {})
            var bait_effects = bait_data.get("effects", {})

            if not rod_effects.has("probability_modifiers"):
                validation_result.warnings.append("鱼竿 %s 缺少probability_modifiers" % rod_id)
            if not bait_effects.has("probability_modifiers"):
                validation_result.warnings.append("鱼饵 %s 缺少probability_modifiers" % bait_id)

            # 检查可钓获等级控制
            if not bait_effects.has("accessible_fish_tiers"):
                validation_result.warnings.append("鱼饵 %s 缺少accessible_fish_tiers" % bait_id)

            # 测试概率计算
            var probabilities = calculate_fishing_probabilities(rod_id, bait_id, "freshwater")
            if probabilities.get("is_valid", false):
                validation_result.probability_calculations_verified += 1
            else:
                validation_result.errors.append("装备组合 %s + %s 概率计算失败" % [rod_id, bait_id])
                validation_result.is_valid = false

            validation_result.equipment_combinations_tested += 1

    return validation_result

## 调试和统计方法 ##
func get_statistics() -> Dictionary:
    """获取配置数据的统计信息"""
    if not is_loaded:
        return {"error": "数据未加载"}

    var stats = {
        "fishing_rods": fishing_rods.size(),
        "baits": baits.size(),
        "water_types": water_types.size(),
        "total_catch_items": 0,
        "catch_items_by_category": {}
    }

    for category in catch_items.keys():
        var category_count = catch_items[category].size()
        stats.catch_items_by_category[category] = category_count
        stats.total_catch_items += category_count

    return stats

func get_equipment_combination_details(rod_id: String, bait_id: String, water_type: String = "freshwater") -> Dictionary:
    """获取装备组合的详细效果信息 - 用于调试和验证"""
    var details = {
        "rod_id": rod_id,
        "bait_id": bait_id,
        "water_type": water_type,
        "is_valid": false,
        "rod_effects": {},
        "bait_effects": {},
        "accessible_tiers": [],
        "probability_breakdown": {},
        "final_probabilities": {}
    }

    var rod_data = get_fishing_rod_data(rod_id)
    var bait_data = get_bait_data(bait_id)

    if rod_data.is_empty() or bait_data.is_empty():
        return details

    details.is_valid = true
    details.rod_effects = rod_data.get("effects", {})
    details.bait_effects = bait_data.get("effects", {})
    details.accessible_tiers = details.bait_effects.get("accessible_fish_tiers", [])

    # 计算概率分解
    var base_probabilities = {
        "0": 0.3, "1": 0.4, "2": 0.2, "3": 0.1
    }

    var rod_modifiers = details.rod_effects.get("probability_modifiers", {})
    var bait_modifiers = details.bait_effects.get("probability_modifiers", {})

    for tier in details.accessible_tiers:
        var tier_str = str(tier)
        var modifier_key = "junk" if tier == 0 else "tier_%d_fish" % tier

        var base_prob = base_probabilities.get(tier_str, 0.0)
        var rod_modifier = rod_modifiers.get(modifier_key, 1.0)
        var bait_modifier = bait_modifiers.get(modifier_key, 1.0)
        var final_prob = base_prob * rod_modifier * bait_modifier

        details.probability_breakdown[tier_str] = {
            "base_probability": base_prob,
            "rod_modifier": rod_modifier,
            "bait_modifier": bait_modifier,
            "final_probability": final_prob
        }

    # 获取完整的概率计算结果
    details.final_probabilities = calculate_fishing_probabilities(rod_id, bait_id, water_type)

    return details

func get_all_catch_items() -> Dictionary:
    """获取所有钓获物数据"""
    return catch_items

## 全局渔业配置管理 - 简化版本 ##
func set_global_fishing_config(rod_id: String, bait_id: String) -> bool:
    """设置全局渔业配置"""
    # 清空配置
    if rod_id.is_empty() or bait_id.is_empty():
        return _clear_global_config()

    # 验证装备存在
    if not _validate_fishing_equipment(rod_id, bait_id):
        return false

    # 更新配置
    global_fishing_config.rod_id = rod_id
    global_fishing_config.bait_id = bait_id
    global_fishing_config.is_valid = true

    global_fishing_config_changed.emit(rod_id, bait_id)
    return true

func _clear_global_config() -> bool:
    """清空全局配置"""
    global_fishing_config.rod_id = ""
    global_fishing_config.bait_id = ""
    global_fishing_config.is_valid = false
    global_fishing_config_changed.emit("", "")
    return false

func _validate_fishing_equipment(rod_id: String, bait_id: String) -> bool:
    """验证渔具装备"""
    var rod_valid = fishing_rods.has(rod_id)
    var bait_valid = baits.has(bait_id)

    if not rod_valid:
        push_warning("FishingDataManager: 鱼竿 '%s' 不存在，可用鱼竿: %s" % [rod_id, fishing_rods.keys()])
    if not bait_valid:
        push_warning("FishingDataManager: 鱼饵 '%s' 不存在，可用鱼饵: %s" % [bait_id, baits.keys()])

    return rod_valid and bait_valid

func get_global_fishing_config() -> Dictionary:
    """获取全局渔业配置"""
    return global_fishing_config.duplicate()

func is_global_fishing_config_valid() -> bool:
    """检查全局渔业配置是否有效"""
    return global_fishing_config.is_valid

func clear_global_fishing_config() -> void:
    """清空全局渔业配置"""
    _clear_global_config()

## 工具方法 - 简化版本 ##
func get_debug_info() -> Dictionary:
    """获取调试信息"""
    return {
        "is_loaded": is_loaded,
        "config_path": CONFIG_PATH,
        "fishing_rods": fishing_rods.size(),
        "baits": baits.size(),
        "catch_items": catch_items.size(),
        "water_types": water_types.size(),
        "global_config_valid": global_fishing_config.is_valid
    }

func print_probability_calculation_debug(rod_id: String, bait_id: String, water_type: String = "freshwater") -> void:
    """打印概率计算的详细调试信息"""
    print("=== 渔夫装备概率计算调试 ===")
    print("装备组合: %s + %s" % [rod_id, bait_id])
    print("水体类型: %s" % water_type)

    var details = get_equipment_combination_details(rod_id, bait_id, water_type)
    if not details.get("is_valid", false):
        print("❌ 装备组合无效")
        return

    print("\n📊 概率分解:")
    var breakdown = details.get("probability_breakdown", {})
    for tier_str in breakdown.keys():
        var tier_data = breakdown[tier_str]
        var tier_name = "杂物" if tier_str == "0" else "%s级鱼" % tier_str
        print("  %s: %.1f%% × %.2f × %.2f = %.2f%%" % [
            tier_name,
            tier_data.base_probability * 100,
            tier_data.rod_modifier,
            tier_data.bait_modifier,
            tier_data.final_probability * 100
        ])

    print("\n🎯 最终结果:")
    var final_probs = details.get("final_probabilities", {})
    print("  总鱼类概率: %.2f%%" % (final_probs.get("total_fish_probability", 0.0) * 100))
    print("  总杂物概率: %.2f%%" % (final_probs.get("total_junk_probability", 0.0) * 100))
    print("  配置有效性: %s" % ("✅" if final_probs.get("is_valid", false) else "❌"))
    print("==========================")
