# 【Gemini】种植系统（农田作物）开发优化方案 (已采纳用户及Claude方案建议进行更新 - 最终指导版)

**编者注：本文档是经过多轮讨论、融合外部方案优点，并根据Boss最新指示调整后的最终开发指导方案。核心聚焦于种植系统的完善，已移除暂缓开发的天气/季节系统相关内容。本文档将作为后续种植系统开发的统一指导。**

## 🚀 快速导航

- [1. 农田浇水视觉强化](#1-农田浇水视觉强化)
- [2. 作物拓展与资源管理 (重点更新)](#2-作物拓展与资源管理-重点更新)
  - [2.1. 新增作物：胡萝卜与土豆](#21-新增作物胡萝卜与土豆)
  - [2.2. 资源路径与命名规范](#22-资源路径与命名规范)
  - [2.3. 作物视觉资源处理方案 (AtlasTexture + 代码驱动)](#23-作物视觉资源处理方案-atlastexture--代码驱动)
  - [2.4. `crops.json` 配置拓展与详解](#24-cropsjson-配置拓展与详解)
- [3. 作物属性、解锁与成长曲线设计 (重点更新)](#3-作物属性解锁与成长曲线设计-重点更新)
  - [3.1. `crops.json` 新增属性详解 (复述)](#31-cropsjson-新增属性详解-复述)
  - [3.2. 作物解锁机制与UI反馈](#32-作物解锁机制与ui反馈)
  - [3.3. 成长曲线设计 (增加过渡层级，兼顾平滑性)](#33-成长曲线设计-增加过渡层级兼顾平滑性)
- [4. 玩家指定种植与农田清理优化](#4-玩家指定种植与农田清理优化)
  - [4.1. `Farmland.gd` 状态与数据调整](#41-farmlandgd-状态与数据调整)
  - [4.2. UI交互：选择作物与指定农田](#42-ui交互选择作物与指定农田)
  - [4.3. 清理农田种植指定](#43-清理农田种植指定)
  - [4.4. `Farmer.gd` AI与任务调整](#44-farmergd-ai与任务调整)
- [5. 作物视觉、生命周期与对齐优化 (Y轴排序说明简化)](#5-作物视觉生命周期与对齐优化-y轴排序说明简化)
  - [5.1. 作物精灵图资源处理与对齐微调](#51-作物精灵图资源处理与对齐微调)
  - [5.2. 作物对齐与Y轴排序 (简化说明与示例)](#52-作物对齐与y轴排序-简化说明与示例)
  - [5.3. 作物完整生命周期（从种植到存储）](#53-作物完整生命周期从种植到存储)
- [6. 农夫角色集成与系统稳定性](#6-农夫角色集成与系统稳定性)
- [7. 水分系统深化与玩法结合 (重点更新)](#7-水分系统深化与玩法结合-重点更新)
  - [7.1. 农夫浇水能力与升级](#71-农夫浇水能力与升级)
  - [7.2. 农田水分消耗与作物生长影响](#72-农田水分消耗与作物生长影响)
  - [7.3. 与天气/季节系统联动 (新)](#73-与天气季节系统联动-新)
- [8. 健壮性与错误处理 (重点更新)](#8-健壮性与错误处理-重点更新)
  - [8.1. 资源加载失败处理](#81-资源加载失败处理)
  - [8.2. 配置缺失处理](#82-配置缺失处理)
  - [8.3. 空引用检查](#83-空引用检查)
- [9. 代码规模与脚本管理](#9-代码规模与脚本管理)
- [10. Godot 4.3 最佳实践遵循](#10-godot-43-最佳实践遵循)
- [11. 玩法循环考量 (新)](#11-玩法循环考量-新)
- [12. 总结与后续步骤](#12-总结与后续步骤)

---

## 一、涉及的脚本与场景文件概览

以下列出了在实施本种植系统优化方案时，主要会涉及到修改或参考的核心脚本和场景文件：

**配置文件：**
*   `config/crops.json`: 核心作物数据配置文件，将包含所有作物的属性、视觉资源路径、解锁条件等。

**场景文件 (`scenes/`):**
*   `scenes/Agriculture/Farmland.tscn`: 农田场景，将修改其视觉表现（干/湿状态）和交互逻辑。
*   `scenes/Agriculture/Crop.tscn`: 作物场景，将适配单一 `Sprite2D` 动态显示生长阶段的方案。
*   可能的UI场景（如主UI、种植选择面板等，具体路径需根据 `UIManager.gd` 的实际管理情况确定）。

**脚本文件 (`scripts/`):**
*   `scripts/agriculture/Farmland.gd`: 农田核心逻辑，包括水分管理、作物指定、状态切换、与农夫交互等。
*   `scripts/agriculture/Crop.gd`: 作物核心逻辑，包括生长周期、视觉更新 (AtlasTexture)、水分消耗、与农田交互等。
*   `scripts/core/DataManager.gd`: 负责加载和提供 `crops.json` 中的配置数据，处理作物解锁条件的检查。
*   `scripts/managers/UIManager.gd`: (或项目统一的UI管理脚本) 负责处理玩家选择作物、指定农田的UI交互逻辑和反馈。
*   `scripts/characters/Farmer.gd` (或农夫行为脚本): 调整农夫AI以适应新的种植指定、收获、浇水（包括能力升级）等逻辑。
*   `scripts/core/GameManager.gd`: 可能用于获取全局管理器实例或协调系统间交互。
*   `scripts/utils/Utilities.gd`: 可能用于添加或使用全局辅助函数。

**请注意：** 根据实际开发中的具体实现，可能还会涉及到对其他相关UI脚本或农夫AI辅助脚本的微调。上述列表为主要核心文件。

---

## 二、详细实施步骤计划

为确保种植系统优化工作有序进行，建议遵循以下分步实施计划：

1.  **资源审查与核心配置 (首要步骤)**:
    *   **1.1. 美术资源确认**: 再次确认所有必要的视觉资源已就绪或已规划清晰：
        *   各作物SpriteSheet (包含所有生长阶段)。
        *   作物UI图标。
        *   农田干/湿状态的Sprite图。
        *   通用的占位符/错误提示纹理 (如红色问号)。
    *   **1.2. `crops.json` 初始配置**:
        *   根据方案中 `2.4` 和 `3.3` 章节的指导，创建并详细配置 `config/crops.json` 文件。
        *   **初期将集中完成小麦、胡萝卜、土豆三种核心作物的完整配置与测试，其他约37种作物的详细数据填充和平衡将在核心机制稳定后逐步推进。**
        *   至少完整配置小麦、胡萝卜、土豆的所有属性，包括 `display_name`, `growth_time_seconds`, `water_needed_total`, `water_consumption_per_second`, `optimal_water_threshold_percentage`, `growth_penalty_multiplier_if_dry`, `yield_resource_type`, `yield_amount`, `planting_cost`, `sell_price_per_unit`, `unlock_condition` (至少包含 `is_unlocked_by_default` 和对前置作物的简单要求), `is_unlocked_by_default`, `description`, `scene_path`。
        *   重点配置这三种作物的 `visuals` 部分：`spritesheet_path`, `icon_source`, `regions` (为每个生长阶段和图标精确定义x, y, w, h及可选margin), `stage_order`。
    *   **1.3. `DataManager.gd` 适配**: 修改 `DataManager.gd` 以能正确加载、解析和提供新的 `crops.json` 数据结构。实现基础的 `get_crop_config(crop_type_id)` 方法。

2.  **作物视觉与基础生命周期 (核心功能)**:
    *   **2.1. `Crop.tscn` 调整**: 确保 `Crop.tscn` 场景使用单一 `Sprite2D` 节点 (`VisualSprite`) 显示作物。
        *   **完成情况**：Boss已确认 `Crop.tscn` 的根节点为 `Node2D` (名为 `Crop`)，其下的 `Sprite2D` 节点（名为 `Sprite2D`，在脚本中通过 `visual_sprite` 引用）已设置 `scale = Vector2(2, 2)` 以适应16px素材。
    *   **2.2. `Crop.gd` 实现**:
        *   **完成情况（已完成核心重构）**：`scripts/agriculture/Crop.gd` 已根据新方案进行了大幅修改。
        *   **核心实现总结**：
            *   **配置驱动**: 
                *   通过 `_GameManager` 获取 `_data_manager` 实例。
                *   核心方法 `initialize_crop(id: String, farmland_ref)` 接收作物ID和农田引用，通过 `_data_manager.get_crop_config(id)` 获取并存储该作物的完整配置到 `_config` 变量。
                *   所有作物行为（生长、耗水、视觉、产量等）均由 `_config` 中的数据驱动。
            *   **动态视觉 (AtlasTexture & SpriteSheet)**:
                *   `_ready()`: 创建一个 `AtlasTexture` 实例 (`_atlas_texture`) 并将其赋给 `visual_sprite.texture`。
                *   `initialize_crop()`: 加载作物配置中 `visuals.spritesheet_path` 定义的主纹理 (`_main_texture`)，并将其设置为 `_atlas_texture.atlas`。
                *   `_update_visuals()`: 此方法是视觉更新的核心。它根据当前生长阶段 (`current_stage_key`)，调用 `_data_manager.get_crop_visual_region(crop_type_id, current_stage_key)` 来获取该阶段在SpriteSheet上的 `Rect2` 区域。
                *   获取到的 `Rect2` 被设置为 `_atlas_texture.region`，从而使 `visual_sprite` 显示正确的图像。Sprite的 `scale` 已在场景中设置为 `(2,2)`。
            *   **生长与阶段管理**:
                *   `_process(delta)`: 处理生长逻辑。根据农田水分 (`current_water_in_farmland`) 和作物配置 (`optimal_water_threshold`, `growth_penalty_if_dry`) 计算 `effective_delta`。
                *   `current_growth_progress` (0-1范围) 和 `current_stage_elapsed_time` 被更新。
                *   `_get_duration_for_stage(stage_key)`: 目前简单地将总生长时间 (`_total_growth_time`) 平均分配给 `_stage_order` 中的每个阶段。
                *   当 `current_stage_elapsed_time` 达到当前阶段应有市场，调用 `_advance_to_next_stage()`。
                *   `_advance_to_next_stage()`: 更新 `current_stage_index` 和 `current_stage_key`，调用 `_update_visuals()` 更新外观，并发出 `growth_stage_changed` 信号。
                *   `_on_growth_completed()`: 当所有阶段完成，标记作物成熟 (`is_mature = true`, `is_harvestable = true`)，发出 `growth_completed` 信号，并通知农田。
            *   **水分系统交互**:
                *   `_process()`: 从父农田 (`_parent_farmland`) 的 `get_current_water()` 获取当前水分。
                *   如果水分低于阈值，则应用生长惩罚。如果严重缺水，发出 `needs_water` 信号。
                *   根据配置的 `_water_consumption_per_second`，调用 `_parent_farmland.consume_water()`。
                *   `apply_water(amount)`: 作物被浇水时调用的方法，发出 `watered` 信号。
                *   `get_water_needs()`: 公共方法，供农田查询作物的需水配置。
            *   **收获与移除**:
                *   `harvest() -> Dictionary`: 检查是否可收获，根据配置计算产量，发出 `harvested` 信号，然后调用 `destroy_crop()`。
                *   `destroy_crop(by_player_action: bool)`: 发出 `removed` 信号，通知农田，然后 `queue_free()` 自身。
            *   **核心公共API/方法**:
                *   `initialize_crop(id: String, farmland_ref) -> bool`: 初始化作物，必须在作物添加到场景后由农田调用。
                *   `harvest() -> Dictionary`: 执行收获。
                *   `destroy_crop(by_player_action: bool)`: 移除作物。
                *   `apply_water(amount: float)`: 外部调用，通知作物被浇水。
                *   `get_water_needs() -> Dictionary`: 获取作物水分需求信息。
                *   `get_type_id() -> String`: 返回作物类型ID。
                *   `set_farmland_reference(farmland)`: 设置父农田的引用。
            *   **信号机制**:
                *   `growth_stage_changed(crop, old_stage_key, new_stage_key)`
                *   `watered(crop, amount)`
                *   `growth_completed(crop)`
                *   `harvested(crop, yield_data)`
                *   `removed(crop)`
                *   `needs_water(crop)`
        *   **待办/注意**：
            *   与 `Farmland.gd` 的交互部分（如 `_parent_farmland.get_current_water()`）依赖于 `Farmland.gd` 中对应方法的实现。
            *   各生长阶段的具体时长目前是平均分配，未来可优化为在 `crops.json` 中精细配置。

3.  **农田浇水视觉与基础水分 (核心功能)**:
    *   **3.1. `Farmland.tscn` 调整**: 
        *   **完成情况**: Boss 已确认 `Farmland.tscn` 中，干燥状态的Sprite节点名为 `DrySprite` (AtlasTexture region 176,0,16,16)，湿润状态的Sprite节点名为 `WetSprite` (AtlasTexture region 192,0,16,16)，两者 `scale` 均为 `(2,2)`。
    *   **3.2. `Farmland.gd` 实现 (已完成核心重构)**:
        *   **核心实现总结**:
            *   **状态管理**: 农田状态由整数枚举改为字符串键 (`current_state_key`)，如 `"EMPTY"`, `"OCCUPIED_GROWING"`, `"OCCUPIED_READY_FOR_HARVEST"`。
            *   **节点引用与视觉**: 
                *   正确引用了 `DrySprite` 和 `WetSprite`。
                *   `_update_farmland_visuals()` 方法根据当前 `water_level` 和 `water_capacity` (默认0.1比例为阈值)切换 `DrySprite` 和 `WetSprite` 的可见性。
            *   **水分基础**: 
                *   实现了基础的水分增减逻辑 (`water_level`, `water_capacity`)。`water_level` 初始为容量的50%。
                *   `water(amount)` 方法用于增加水分，并会调用 `_update_water_display()` 和 `_update_farmland_visuals()`。
            *   **UI更新**:
                *   `_update_water_display()` 更新水分进度条的显示。
                *   `_update_status_display()` 更新状态标签。
                *   `_update_growth_progress_bar()` 更新生长进度条的显隐和数值（从 `current_crop` 获取）。
                *   `_update_all_visuals()` 统一调用所有视觉更新方法。
            *   **管理器初始化**: `_initialize_managers()` 用于获取 `_GameManager`, `_DataManager`, `_UIManager` 的引用。
            *   **日志与辅助**: 保留了日志系统，并提供了 `get_state_display_name`, `get_interaction_point`, `get_interaction_data`, `get_details_for_ui` 等辅助方法。
        *   **待办/注意**: 
            *   `_process` 中水分消耗、作物生长修正、与 `Crop.gd` 的深度交互将在步骤4中进一步完善。
            *   玩家指定种植、清理等交互逻辑将在步骤5中实现。

4.  **水分系统深化 (作物关联)**:
    *   **4.1. `Farmland.gd` (已完成核心重构，集成此部分功能)**:
        *   **核心实现总结**:
            *   在 `_process(delta)` 中，如果农田上有作物且作物配置有效：
                *   根据当前作物配置的 `water_consumption_per_second` 减少农田的 `water_level`。
                *   计算当前水分百分比 (`current_water_percentage`)。
                *   根据作物配置的 `optimal_water_threshold_percentage` 和 `growth_penalty_multiplier_if_dry`，计算出实际的 `growth_modifier`。
                *   调用 `current_crop.set_external_growth_modifier(growth_modifier)` 将此修正系数传递给作物实例。
            *   `water(amount)` 方法会调用 `current_crop.apply_water(amount)` (如果作物实例有效)。
    *   **4.2. `Crop.gd**:
        *   **完成情况 (已在步骤2.2中实现)**: `Crop.gd` 中已添加 `set_external_growth_modifier(modifier: float)` 方法，并在其 `_process(delta)` 中应用此修正系数到生长速率计算。
    *   **4.3. 农夫浇水能力 (`Farmer.gd`)**:
        *   **完成情况 (已完成核心重构)**:
            *   `scripts/characters/types/Farmer.gd` 已根据新方案调整。
            *   **核心实现总结**:
                *   **浇水属性**: 定义了农夫单次基础浇水量 (`base_water_amount_per_action`)、浇水技能等级 (`watering_skill_level`) 和每级浇水效率提升 (`watering_efficiency_per_level`)。
                *   **有效浇水量计算**: 实现了 `get_effective_watering_amount()` 方法，综合基础量、技能等级和效率提升，计算出农夫单次行动的实际浇水量。
                *   **与农田交互**: 农夫执行浇水动作时，会调用目标农田的 `water()` 方法，并传递其计算出的有效浇水量。

5.  **玩家指定种植与农田清理 (核心功能)**:
    *   **5.1. UI交互调整 (作物选择与金币消耗)**:
        *   **完成情况（已完成核心重构）**: UI交互逻辑已进行调整，不再创建单独的作物选择面板。
        *   **核心实现总结**:
            *   **`MainUI.gd`**:
                *   添加了 `class_name MainUI`。
                *   管理玩家当前选择的待种植作物ID (`selected_crop_for_planting_id`)，提供了 `set_selected_crop_for_planting(crop_id: String)`、`get_selected_crop_for_planting_id() -> String` 和 `clear_selected_crop_for_planting()` 方法。
                *   获取 `DataManager` 和 `ResourceManager` 的引用，用于后续检查作物可种植性及消耗。
            *   **`ItemSlot.gd` 和 `ItemSlot.tscn` (用于资源显示中的作物条目)**:
                *   `ItemSlot.gd` 支持通过字符串ID (`item_id`) 来标识作物。
                *   添加了可选/选中状态的逻辑和视觉反馈（例如，选中时改变背景色或显示边框），用于标记玩家选择的待种植作物。
                *   发出 `item_clicked(item_id, self_instance)` 信号。
            *   **`ResourceDisplay.gd` (假设用于显示可种植作物列表)**:
                *   修改为从 `DataManager.get_plantable_crop_ids()` 获取可种植的作物列表。**因此，在当前阶段，UI上只会显示已解锁的可种植作物。未解锁的作物在满足其解锁条件（例如拥有足够的前置作物）之前，不会出现在此列表中。**
                *   为每个可种植作物实例化 `ItemSlot.tscn`。
                *   从 `DataManager.get_crop_icon_texture_for_ui(crop_id)` 获取作物图标并设置给 `ItemSlot`。
                *   连接 `ItemSlot` 的 `item_clicked` 信号，当玩家点击某个作物 `ItemSlot` 时，调用 `MainUI.set_selected_crop_for_planting(crop_id)`。
                *   根据 `MainUI` 中当前选中的作物，更新对应 `ItemSlot` 的选中状态视觉。
            *   **种植成本**: 种植作物的金币成本检查和扣除逻辑已集成到 `Farmland.gd` 的 `plant_crop(crop_type_id: String)` 方法中，该方法会从 `DataManager` 获取作物配置中的 `planting_cost`，并与 `ResourceManager` 交互。
    *   **5.2. `Farmland.gd` (已完成核心重构，集成此部分功能)**:
        *   **核心实现总结**:
            *   添加 `assigned_crop_type_id: String` 变量存储玩家指定的作物ID。
            *   在 `_ready()` 中动态创建 `assigned_marker_sprite: Sprite2D` 用于显示指定作物的图标。
            *   重写 `_on_interaction_area_input_event`：
                *   鼠标左键：如果UI已选择作物 (通过 `_ui_manager.get_selected_crop_for_planting()`)，则调用 `assign_crop()`。否则，调用 `show_farmland_info()`。
                *   鼠标右键：调用 `handle_right_click_action()`。
            *   `handle_right_click_action()` 逻辑：如果农田上有作物，调用 `clear_crop_on_farmland(true)`；如果没有作物但有指定，调用 `cancel_crop_assignment()`。
            *   `assign_crop(crop_type_id)`: 检查作物是否解锁。如果农田为空或无作物，则设置 `assigned_crop_type_id` 并更新标记；如果农田有作物但想更改指定，也更新标记（不影响当前作物）。通知AI系统有新的指定。
            *   `cancel_crop_assignment()`: 清除 `assigned_crop_type_id` 并隐藏标记。
            *   `clear_crop_on_farmland(by_player_action: bool)`: 调用 `current_crop.destroy_crop()`，然后调用 `cancel_crop_assignment()`，最后将农田状态改为 `"EMPTY"`。如果由玩家触发，会提示资源不返还。
            *   `_update_assigned_marker()`: 根据 `assigned_crop_type_id` 和 `_data_manager.get_crop_icon_region_data()` 获取作物图标的 `AtlasTexture` 数据并更新 `assigned_marker_sprite`。
            *   `inform_ai_about_new_assignment()`: 用于（未来）通知AI系统有新的指定任务。
            *   `show_farmland_info()`: 用于（未来）通过 `_ui_manager` 显示农田详情。
    *   **5.3. `Farmer.gd` 调整**:
        *   **完成情况 (已完成核心重构)**:
            *   `scripts/characters/types/Farmer.gd` 已根据新方案调整，并与 `FarmerTaskManager.gd` 紧密协作。
            *   **核心实现总结**:
                *   **处理指定种植**: 农夫AI (主要通过 `FarmerTaskManager.gd` 的决策) 在寻找种植任务时，会优先检查农田的 `assigned_crop_type_id`。如果农田为空闲且有指定作物，农夫会尝试种植该指定作物。
                *   **种植成本检查**: `FarmerTaskManager.gd` 在创建种植任务前，会从 `DataManager` 获取作物种植成本，并向 `ResourceManager` 查询玩家是否有足够的金币。若不足，则任务不创建。
                *   **任务中断与状态同步**: 调整了农夫的状态管理，以更好地处理因玩家操作（如清除指定）导致的任务变化，确保农夫能返回空闲并重新评估任务。

6.  **作物完整生命周期集成 (种植到收获)**:
    *   **6.1. `Farmland.gd` (已完成核心重构，大部分集成)**:
        *   **核心实现总结**:
            *   `plant_crop(crop_type_id)`: 实例化作物场景，调用 `crop_instance.initialize_crop(crop_type_id, self)`，设置作物到正确的YSort层 (通过 `_game_manager.get_y_sort_node()`)，连接作物信号，并将农田状态切换为 `"OCCUPIED_GROWING"`。如果此次种植的是指定作物，则清除指定。
            *   `_on_crop_growth_completed(crop)`: 当接收到作物的 `growth_completed` 信号，将农田状态切换为 `"OCCUPIED_READY_FOR_HARVEST"` 并发出 `ready_for_harvest` 信号。
            *   `_on_crop_harvested(crop, harvest_result)`: 记录日志，主要依赖 `_on_crop_removed` 处理后续。
            *   `_on_crop_removed(crop)`: 当接收到作物的 `removed` 信号，清除 `current_crop` 引用。如果农田之前有指定作物，则重置状态为 `"EMPTY"` 并通知AI尝试重新种植；否则仅重置为 `"EMPTY"`。
            *   `_on_crop_needs_water(crop)`: 记录日志（此信号主要由农夫监听）。
            *   `_on_crop_growth_stage_changed(crop, old_key, new_key)`: 更新生长进度条。
            *   `harvest()`: 调用 `current_crop.harvest()`，作物自身会处理后续的移除。
    *   **6.2. `Crop.gd` (已在步骤2.2完成)**: `initialize_crop()`, `_process()` (生长和阶段更新), `harvest()` (计算产量、发射信号、调用 `destroy_crop()`), `destroy_crop()` (发射信号、`queue_free()`) 逻辑已基本完整。
    *   **6.3. `Farmer.gd` (已完成核心重构)**:
        *   **核心实现总结**:
            *   **种植动作**: 农夫AI（通过 `FarmerTaskManager.gd` 和 `FarmerInteractionManager.gd`）在接收到种植任务后，会移动到目标农田，播放种植动画，并调用 `Farmland.gd` 的 `plant_crop()` 方法（该方法内部处理成本扣除）。
            *   **收获动作**: 农夫能识别可收获的农田，移动前往，播放动画，并调用 `Farmland.gd` 的 `harvest()` 方法。
            *   **携带资源**:
                *   收获成功后，根据作物配置的产量和类型，调用自身的 `set_carrying_resource(resource_type, amount)` 方法。
                *   `set_carrying_resource` 会更新内部携带状态，并调用 `_show_carrying_icon()` 在农夫头顶显示携带的资源图标。
            *   **存储交互**:
                *   携带资源后，`FarmerTaskManager.gd` 会指派存储任务，农夫移动到最近的存储建筑。
                *   通过 `FarmerInteractionManager.gd` 与存储建筑交互，成功后调用自身的 `clear_carrying_resource()` 方法清除携带状态并隐藏图标。
                *   农夫状态变为空闲，准备接收新任务。
            *   **三管理器协作**: `Farmer.gd` 的大部分行为由其内部的 `_task_manager`, `_interaction_manager`, 和 `_timer_manager` 驱动和管理，确保了任务执行的条理性和对各种游戏事件的响应。

7. **作物解锁机制与UI反馈 (当前阶段采用简化方案)**:

**重要说明**：Boss指示，当前阶段作物解锁机制与UI反馈**采取简化方案**。完整的UI重构（包括未解锁作物的详细展示逻辑和复杂的解锁条件反馈）将在后续其他工作中集中进行。以下步骤描述当前的简化实现。
    **7.1. `DataManager.gd` (简化解锁逻辑)**
*   `is_crop_unlocked(crop_type_id: String) -> bool` 方法:
    *   **当前实现**：按计划，仅检查作物的 `is_unlocked_by_default` 属性，以及 `unlock_condition` 中是否满足简化的 `{"requires_crop": "<some_crop_id>", "amount": <N>}` 条件。此检查已能访问 `ResourceManager`。
        ```json
        // unlock_condition 示例 (当前仅处理此简化形式)
        {
            "requires_crop": "wheat", 
            "amount": 10
        }
        ```
    *   **暂缓开发**：更复杂的解锁条件（如玩家等级、特定建筑需求、多条件组合等）的检查逻辑将暂缓，待后续统一规划。
*   `get_plantable_crop_ids() -> Array[String]` 方法:
    *   **当前实现**：按计划，此方法基于上述简化的 `is_crop_unlocked` 逻辑来过滤并返回所有当前玩家已解锁且可种植的作物ID列表。

    **7.2. UI反馈 (简化版 - 配合后续重构)**
*   **`ResourceDisplay.gd` (及相关UI脚本)**:
    *   **当前实现**：按计划，作物选择界面（如 `ResourceDisplay.gd`）直接使用 `DataManager.get_plantable_crop_ids()` 获取并显示作物列表。因此，UI上仅会列出当前已解锁且可种植的作物。
    *   **后续UI重构说明**：当前不实现未解锁作物的特殊UI显示（例如，灰色、锁定图标、解锁条件提示等）。这些功能以及更复杂的解锁条件反馈，将在后续专门的UI重构阶段统一设计和实现。
    *   **新解锁作物表现**：当一个作物通过简化的解锁逻辑变为可种植时，它会自然出现在UI的可种植列表中。此时其在 `ResourceManager` 中的数量为0，符合预期。一次性的UI通知或高亮等效果也暂缓，待UI重构时考虑。


8.  **健壮性、对齐与整体测试**:
    *   **8.1. 错误处理**: 在所有关键路径（资源加载、配置读取、节点查找、方法调用）严格实施方案第8节中的健壮性措施。
        *   **`Farmland.gd` 和 `Crop.gd` 已在重构中加入大量 `is_instance_valid` 检查和日志。**
    *   **8.2. Y轴排序与对齐**: 仔细检查作物、农夫等对象在 `YSort` 节点下的排序表现，微调 `AtlasTexture` 的 `margin` 或 `VisualSprite` 的 `offset` 以确保所有作物各阶段底部正确对齐。
        *   **`Farmland.gd` 的 `plant_crop` 已加入YSort处理逻辑。**
    *   **8.3. 全面测试**:
        *   **待办**: 在 `Farmer.gd` 和 UI 相关功能完成后进行。测试所有已配置作物的完整生命周期。测试农田指定、清理、农夫AI的各种交互场景。测试水分系统对不同作物生长的影响。测试作物解锁流程。特别关注边缘情况和多农夫协同工作的稳定性。
        *   **测试作物解锁流程**:
            1.  **配置 `config/crops.json`**：
                *   确保**小麦(wheat)** 设置为 `"is_unlocked_by_default": true`。
                *   配置**胡萝卜(carrot)** 为 `"is_unlocked_by_default": false`，并设置 `"unlock_condition": {"requires_crop": "wheat", "amount": 10}`。
                *   配置**土豆(potato)** 为 `"is_unlocked_by_default": false`，并设置 `"unlock_condition": {"requires_crop": "carrot", "amount": 5}` (或其他适当数量)。
            2.  **游戏初期验证**：启动游戏，打开种植选择界面 (`ResourceDisplay`)。此时应该只能看到并选择小麦进行种植。
            3.  **解锁胡萝卜**：通过种植和收获小麦，直到 `ResourceManager` 中小麦的数量达到或超过10个。
            4.  **验证胡萝卜解锁**：再次打开或刷新种植选择界面。此时胡萝卜应该出现在可种植列表中，其数量为0。
            5.  **解锁土豆**：种植并收获胡萝卜，直到 `ResourceManager` 中胡萝卜的数量达到或超过5个。
            6.  **验证土豆解锁**：再次打开或刷新种植选择界面。此时土豆应该出现在可种植列表中，数量为0。
            7.  **验证种植**：测试新解锁的胡萝卜和土豆是否可以被正常选择、指定到农田并由农夫种植。
        *   特别关注边缘情况和多农夫协同工作的稳定性。

9.  **迭代与优化**: 根据测试结果和实际体验，对数值、交互、性能等进行调优。

**(原方案第12节后续内容，如"Boss，请您审阅这份更新后的指导方案..."等对话性质的总结，将移至实际交流中，文档本身保持方案的纯粹性)**

---

## 1. 农田浇水视觉强化

**需求**：使用两种不同的`Sprite2D`来表现农田的常规状态及浇水后的湿润状态。

**方案**：

1.  **场景修改 (`Farmland.tscn`)**:
    *   保留现有 `Sprite2D` (例如 `sprite` 或重命名为 `DrySprite`) 作为常规状态。
    *   新增一个 `Sprite2D` 节点 (例如 `WetSprite`)，用于显示浇水后的湿润状态纹理。
    *   初始 `WetSprite.visible = false`，`DrySprite.visible = true`。

2.  **脚本逻辑 (`Farmland.gd`)**:
    *   添加对 `DrySprite` 和 `WetSprite` 的引用：
        ```gdscript
        @onready var dry_sprite: Sprite2D = $DrySprite 
        @onready var wet_sprite: Sprite2D = $WetSprite 
        ```
    *   在 `_update_water_display()` 或新建的 `_update_farmland_visuals()` 方法中，根据 `water_level` 和 `water_capacity` 切换Sprite的可见性。
        *   例如，当 `water_level / water_capacity > 0.1` 时，显示 `WetSprite`，隐藏 `DrySprite`，反之亦然。
        ```gdscript
        # In Farmland.gd
        func _update_farmland_visuals() -> void:
            if not is_instance_valid(dry_sprite) or not is_instance_valid(wet_sprite):
                return

            if water_level / water_capacity > 0.1: // 可调整的阈值
                dry_sprite.visible = false
                wet_sprite.visible = true
            else:
                dry_sprite.visible = true
                wet_sprite.visible = false
        ```
    *   在水分变化的逻辑处（如 `water()` 方法和 `_process()` 中水分减少后）调用 `_update_farmland_visuals()`。

**补充建议**：
*   确保提供的两种农田状态图片在尺寸和中心对齐上保持一致，以便切换时不会发生视觉跳动。
*   可以考虑在 `DrySprite` 和 `WetSprite` 之间增加一个"半湿润"状态的Sprite，用于更平滑的视觉过渡，但这会增加资源需求和逻辑复杂度。

## 2. 作物拓展与资源管理 (重点更新)

**需求**：拓展胡萝卜(carrot)和土豆(potato)等新作物，设计易于扩展（支持约40种作物）的资源管理方案，实现各作物4个生长阶段视觉和1个图标。

### 2.1. 新增作物：胡萝卜与土豆

我们将以这两种新作物作为模板进行后续的系统设计，确保方案的可行性。

### 2.2. 资源路径与命名规范

*   **原始资源路径示例**: `res://assets/Hana Caraka/Hana Caraka - Farming n Foraging/crops/` (假设这里面有 `carrot_spritesheet.png`, `potato_spritesheet.png` 等或一个包含所有作物的 `crops_all.png`)
*   **推荐整理后项目内路径**: 为了清晰管理，如果原始资源是分散的，建议在项目中统一存放，例如 `res://assets/textures/crops_spritesheets/` 存放各作物的SpriteSheet。图标可以放在 `res://assets/textures/icons/crops/`。
*   **命名规范 (以胡萝卜为例)**:
    *   **SpriteSheet (推荐)**: `carrot_sheet.png` (包含所有生长阶段)。
    *   **UI/携带图标**: `carrot_icon.png` (如果图标独立于SpriteSheet)。

### 2.3. 作物视觉资源处理方案 (AtlasTexture + 代码驱动)

**采纳建议**：为简化资源管理并灵活处理不同作物的 SpriteSheet，同时保持场景结构简洁高效，我们坚定采用 **单一 `Sprite2D` 节点配合 `AtlasTexture` 并通过代码动态加载/切换** 的方案。这避免了为每种作物或每个生长阶段创建多个节点或独立的 `SpriteFrames` 资源文件。

**方案步骤**：

1.  **SpriteSheet 准备**: 
    *   理想情况下，每种作物的所有生长阶段视觉整合到一个 SpriteSheet 图片文件中 (例如 `wheat_sheet.png`, `carrot_sheet.png`)。 
    *   如果所有作物美术资源都在一个巨大的 SpriteSheet (例如 `crops_all.png`)，本方案同样适用。

2.  **`crops.json` 配置**: 在 `config/crops.json` 中为每种作物定义其 SpriteSheet 路径以及每个生长阶段 (通常4个：seed, growing1, growing2, mature) 和图标在此 SpriteSheet 上的 `region` (Rect2 - x, y, width, height) 和可选的 `margin` (Rect2 - 用于微调对齐)。
    ```json
    // In crops.json, for a specific crop, e.g., "wheat"
    "wheat": {
        // ... (other properties like growth_time, cost, etc.) ...
        "visuals": {
            "spritesheet_path": "res://assets/textures/crops_spritesheets/wheat_sheet.png",
            "icon_source": { // 图标来源定义
                 "type": "spritesheet", // 或 " samostat_path"
                 "path_or_region_key": "icon_main", // 如果type是spritesheet，这是下面regions的key；如果是path，则是完整路径
            },
            "regions": { // 定义spritesheet上的区域块
                "icon_main": {"x": 0, "y": 0, "w": 32, "h": 32},
                "seed":      {"x": 32, "y": 0, "w": 32, "h": 32, "margin": {"l":0,"t":8,"r":0,"b":0}},
                "growing1":  {"x": 64, "y": 0, "w": 32, "h": 32, "margin": {"l":0,"t":6,"r":0,"b":0}},
                "growing2":  {"x": 0,  "y": 32,"w": 32, "h": 32, "margin": {"l":0,"t":4,"r":0,"b":0}},
                "mature":    {"x": 32, "y": 32,"w": 32, "h": 32}
            },
            "stage_order": ["seed", "growing1", "growing2", "mature"] // 定义生长阶段对应的region key顺序
        }
    }
    ```
    *   `icon_source`: 定义图标的获取方式。`type: "spritesheet"` 表示图标也从本条目的 `spritesheet_path` 及 `regions` 中获取；`type: "standalone_path"` 则 `path_or_region_key` 应为完整的独立图标文件路径。
    *   `regions`: 一个字典，键是自定义的区域名（如 "seed", "icon_main"），值是该区域的 `region` 和可选 `margin`。
    *   `stage_order`: 一个数组，按顺序定义了生长阶段所使用的 `regions` 中的键名。这提供了灵活性，即使 `regions` 中的键名不按数字顺序。

3.  **`Crop.gd` 脚本逻辑**:
    *   在 `Crop.gd` 中，使用 ***单个*** `Sprite2D` 节点 (例如 `VisualSprite`) 来显示作物。确保该节点在 `Crop.tscn` 场景中的层级和位置适宜。
    *   `initialize()` 方法中：
        *   从配置读取 `visuals.spritesheet_path` 并 `load()` 主纹理 `_main_crop_texture`。进行错误处理（见第8节）。
        *   后续需要显示某个阶段时，调用一个辅助函数动态创建或从缓存获取 `AtlasTexture`。
            ```gdscript
            # In Crop.gd
            @onready var visual_sprite: Sprite2D = $VisualSprite
            var _main_crop_texture: Texture2D
            var _crop_visual_config: Dictionary
            var _atlas_textures_cache: Dictionary = {} // 缓存AtlasTexture实例

            func initialize(crop_type_id: String, config: Dictionary) -> bool:
                # ... (set crop_type, _crop_config = config, etc.)
                _crop_visual_config = config.get("visuals", {}) # DataManager 仍然可以传递 visuals 部分
                if not _load_main_texture(_crop_visual_config.get("spritesheet_path", "")):
                    _is_initialized = false
                    return false // 主纹理加载失败，初始化失败
                # ... (rest of initialization) ...
                _update_appearance() // Set initial visual state (seed)
                _is_initialized = true
                return true

            func _load_main_texture(path: String) -> bool:
                if path.is_empty() or not ResourceLoader.exists(path):
                    printerr("Crop spritesheet path empty or not found for %s: %s" % [crop_type, path])
                    _main_crop_texture = load("res://assets/textures/placeholder_error.png") 
                    return false // 使用占位符，但标记为失败，除非特定逻辑允许
                _main_crop_texture = load(path)
                return true

            func _get_or_create_atlas_for_stage_key(key_name: String) -> AtlasTexture:
                if not is_instance_valid(_data_manager):
                    printerr("DataManager instance is not valid in Crop.gd for " + crop_type)
                    return _get_placeholder_atlas() // 返回占位符

                # 调用DataManager获取解析后的区域和边距数据
                var visual_data = _data_manager.get_crop_visual_region_data(crop_type, key_name)
                if not visual_data or not visual_data.has("region"):
                    printerr("Failed to get visual region data for crop: %s, key: %s" % [crop_type, key_name])
                    return _get_placeholder_atlas()

                if _atlas_textures_cache.has(key_name):
                    return _atlas_textures_cache[key_name]

                if not is_instance_valid(_main_crop_texture):
                    log_message("Main crop texture not valid for " + crop_type, LogLevel.ERROR)
                    if not _load_main_texture(_crop_visual_config.get("spritesheet_path", "")): // _crop_visual_config 仍可用于此路径
                         return _get_placeholder_atlas()
                
                var region_rect_dict = visual_data.get("region")
                var margin_dict = visual_data.get("margin", null) // margin是可选的

                var atlas = AtlasTexture.new()
                atlas.atlas = _main_crop_texture
                atlas.region = Rect2(region_rect_dict.x, region_rect_dict.y, region_rect_dict.w, region_rect_dict.h)
                if margin_dict and typeof(margin_dict) == TYPE_DICTIONARY and margin_dict.has("l"): // 确保margin_dict有效
                    atlas.margin = Rect2(margin_dict.l, margin_dict.t, margin_dict.r, margin_dict.b)
                
                _atlas_textures_cache[key_name] = atlas
                return atlas
            
            func _get_placeholder_atlas() -> AtlasTexture:
                # 辅助函数，用于获取一个指向错误占位符的AtlasTexture
                var placeholder_atlas = AtlasTexture.new()
                placeholder_atlas.atlas = load("res://assets/textures/placeholder_error.png") 
                placeholder_atlas.region = Rect2(0,0,32,32) // Assuming placeholder is 32x32
                return placeholder_atlas

            func _update_appearance() -> void:
                if not _is_initialized or not is_instance_valid(visual_sprite):
                     # Optionally hide sprite or show placeholder if not initialized
                     if is_instance_valid(visual_sprite): visual_sprite.visible = false 
                     return
                
                visual_sprite.visible = true // Ensure visible if initialized
                var stage_order = _crop_visual_config.get("stage_order", [])
                var current_visual_idx = clamp(int(growth_progress * stage_order.size()), 0, stage_order.size() - 1)
                if current_visual_idx < 0 or current_visual_idx >= stage_order.size():
                    printerr("Calculated visual index out of bounds for " + crop_type)
                    visual_sprite.texture = load("res://assets/textures/placeholder_error.png")
                    return
                
                var stage_key = stage_order[current_visual_idx]
                var atlas_tex = _get_or_create_atlas_for_stage_key(stage_key)
                
                if is_instance_valid(atlas_tex):
                    visual_sprite.texture = atlas_tex
                else: # Fallback if atlas_tex is null or invalid
                    visual_sprite.texture = load("res://assets/textures/placeholder_error.png")
                    printerr("Failed to get/create atlas for %s, stage_key: %s" % [crop_type, stage_key])

                # 逻辑生长阶段更新 (与视觉阶段可能不同，但通常一致)
                var new_logic_stage = current_visual_idx 
                if new_logic_stage != growth_stage:
                    var old_logic_stage = growth_stage
                    growth_stage = new_logic_stage
                    growth_stage_changed.emit(self, old_logic_stage, growth_stage)
            ```
    *   这样，每个作物实例只在需要时创建并缓存其 `AtlasTexture`，有效管理内存和资源。

**图标获取**: 农夫携带图标、UI显示图标等，也可以通过类似的逻辑从 `visuals.icon_source` 和 `visuals.regions` 中获取对应的 `AtlasTexture`。

### 2.4. `crops.json` 配置拓展与详解

在 `config/crops.json` 的每个作物条目中包含以下属性：

*   `display_name`: (String) UI显示名称。
*   `growth_time_seconds`: (Float) 成熟总秒数。
*   `water_needed_total`: (Float) 生长周期总需水量。
*   `water_consumption_per_second`: (Float) 每秒耗水。可由 `water_needed_total / growth_time_seconds` 计算，或直接配置以微调。
*   `optimal_water_threshold_percentage`: (Float, 0.0-1.0) 农田水分低于此百分比，作物生长将减速。
*   `yield_resource_type`: (String) 收获时产生的资源ID（需要与您的物品/资源系统对应）。
*   `yield_amount`: (Dictionary) `{"min": Int, "max": Int}` 定义单次收获的随机产量范围。
*   `planting_cost`: (Int) 种植该作物需要消耗的金币数量。
*   `sell_price_per_unit`: (Int) 每单位收获的作物可以卖出的金币数量。
*   `unlock_condition`: (Dictionary) 解锁条件 (详见下节)。
*   `is_unlocked_by_default`: (Bool) 是否初始默认解锁。
*   `visuals`: (Dictionary) 包含上面 `2.3` 节描述的 `spritesheet_path`, `icon_source`, `regions`, `stage_order`。
*   `description`: (String) 作物的简短描述，可用于UI提示。
*   `scene_path`: (String) 指向该作物使用的场景文件，目前统一为 `res://scenes/Agriculture/Crop.tscn`。

## 3. 作物属性、解锁与成长曲线设计 (重点更新)

### 3.1. `crops.json` 新增属性详解 (复述)
已在 `2.4` 中详细列出，这些属性将驱动新作物的行为和经济体系。

### 3.2. 作物解锁机制与UI反馈

1.  **解锁检查 (`DataManager.gd` 或独立的 `CropUnlockManager.gd`)**:
    *   实现 `is_crop_unlocked(crop_type_id: String) -> bool` 方法。该方法读取 `crops.json` 中对应作物的 `is_unlocked_by_default` 和 `unlock_condition`。
    *   **当前阶段简化实现**：
        *   首先检查 `is_unlocked_by_default`。如果为 `true`，则作物已解锁。
        *   如果 `is_unlocked_by_default` 为 `false`，则检查 `unlock_condition`。目前仅处理形如 `{"requires_crop": "<crop_id>", "amount": <N>}` 的条件。
        *   例如：`"unlock_condition": {"requires_crop": "wheat", "amount": 10}` 表示需要玩家在 `ResourceManager` 中拥有至少10个小麦才能解锁此作物。
        *   `DataManager` 在执行此检查时，需要能够访问 `ResourceManager` 来查询玩家当前拥有的资源数量。
        *   其他类型的解锁条件（如 `player_level`, `requires_building` 或更复杂的组合条件 `{"and": [...]}`) 将在后续版本中考虑。
    *   `unlock_condition` 示例 (当前仅关注第一种)：
        *   `{"requires_crop": "wheat", "amount": 10}`: 需要拥有10个小麦。
        *   `{}`: 无特殊条件 (依赖 `is_unlocked_by_default`)。 (未来可支持)
        *   `{"player_level": 5}`: 需要玩家达到5级。 (暂缓)
        *   `{"requires_building": "greenhouse", "level": 2}`: 需要2级温室。 (暂缓)
        *   可以组合条件: `{"and": [{"requires_crop": "carrot", "amount": 20}, {"player_level": 10}]}` (暂缓)
    *   此检查方法需要能访问玩家资源、等级等全局状态。

2.  **UI反馈**:
    *   在选择种植作物的UI界面 (`ResourceDisplay.gd` 使用 `ItemSlot`):
        *   **当前阶段**：`ResourceDisplay.gd` 通过调用 `DataManager.get_plantable_crop_ids()` 来获取作物列表，此方法内部已基于简化的解锁逻辑进行了过滤，因此UI上将只显示当前已解锁且可种植的作物。**未解锁的作物（例如，需要10个小麦才能解锁的胡萝卜）在玩家满足其 `unlock_condition` 之前，不会出现在种植选择列表中。**
        *   **解锁后的表现**：一旦作物（如胡萝卜）的解锁条件满足（例如，玩家在 `ResourceManager` 中拥有了10个小麦），下一次 `ResourceDisplay.gd` 刷新列表时，胡萝卜便会通过 `get_plantable_crop_ids()` 被包含进来，并显示在UI上。此时，胡萝卜在 `ResourceManager` 中的数量为0，直到玩家成功种植并收获。这个流程允许直接测试解锁机制的效果。
        *   **未来扩展**：如果需要显示所有作物（包括未解锁的），则对于未解锁的作物，`ItemSlot` 应显示为灰色、带锁图标，并可能在鼠标悬停时显示其（简化的）解锁条件。
    *   当作物解锁条件首次满足时，UI应自动更新（例如，下次打开种植选择界面时，该作物变为可选）。可以考虑一个一次性的视觉提示（例如，图标高亮/动画，或一个通知），告知玩家该作物已可用。

### 3.3. 成长曲线设计 (增加过渡层级，兼顾平滑性)

**采纳建议**：在设计约40种作物的成长曲线时，特别注意增加足够的中间过渡作物，以平滑玩家的体验曲线，避免数值跳跃过大。**核心调整：大幅拉伸高阶作物的各项数值，尤其是生长时间，以符合长周期放置游戏特性，并为后期的Buff系统留出显著的优化空间。**

**当前阶段重点**：我们将确保小麦、胡萝卜、土豆三种作物的成长数据合理且功能完整，并通过它们验证系统的核心机制。其他约37种作物的数值平衡、详细的成长曲线设计和高阶作物的复杂解锁链将在核心功能稳定后，作为后续迭代的重点内容逐步完善和填充。下方表格中的高阶作物数值仅为未来方向性参考。

| 属性名 (部分)            | 小麦 (初始) | 甜菜 (早期) | 胡萝卜 (中期) | 南瓜 (中后期) | 土豆 (后期)  | ... | 魔法玉米(高阶) | ... | 水晶甜瓜(顶阶) | ... | 龙之果 (终局)    |
| :------------------------- | :---------- | :---------- | :----------- | :------------ | :---------- | :-- | :------------- | :-- | :------------- | :-- | :------------- |
| `planting_cost`            | 1           | 5           | 10           | 25            | 50          | ... | 500 - 1000     | ... | 15000 - 30000  | ... | 50000 - 100000 |
| `growth_time_seconds`      | 20s         | 60s         | 2m (120s)    | 5m (300s)     | 10m (600s)  | ... | 30m - 1h (1800s - 3600s) | ... | 4h - 8h        | ... | 12h - 24h      |
| `sell_price_per_unit`      | 2           | 3           | 4            | 7             | 10          | ... | 60 - 120       | ... | 1800 - 3500    | ... | 6000 - 10000   |
| `water_needed_total`       | 5           | 10          | 15           | 25            | 40          | ... | 100 - 200      | ... | 500 - 800      | ... | 1000 - 1500    |

**设计原则**:
*   **平滑过渡与显著层级**: 在关键的跳跃点之间（例如成本、时间、解锁难度），插入1-3种属性介于两者之间的过渡作物。确保每个阶段玩家都有可追求且可达成的目标。高阶作物的数值应与初/中期作物有显著区别，体现其价值和获取难度。
*   **多维度解锁**: 早期作物通过少量前置作物解锁，中期引入玩家等级、少量特殊资源需求，后期则可能需要特定稀有建筑、完成系列任务或收集多种高级作物。
*   **收益率与风险**: 仔细用电子表格模拟每种作物的"每小时净收益"和"投资回报周期"。高阶作物应有更高的潜在收益，但也伴随更高的投入风险（如更长的生长时间，对水分等环境因素更敏感）。允许一些"经济作物"和"任务/特殊用途作物"并存。
*   **Buff空间**: **高阶作物的基础数值（尤其是时间）设置得较高，是为后续的全局Buff、科技研究、特殊道具（如高效肥料、自动浇灌装置）提供显著的优化效果，增强玩家获得Buff的成就感。** 例如，一个基础生长时间为8小时的作物，通过Buff减少到4小时，玩家会有强烈的满足感。
*   **放置体验**: 后期作物（例如最后5-10种）可以有非常长的成熟时间（数小时到一天），强化放置玩法。这些作物通常伴随非常复杂的解锁条件和极高的单次收益。

## 4. 玩家指定种植与农田清理优化

**需求**：玩家可以选择特定作物在特定农田种植，农夫按指定执行；玩家可以清理农田的种植指定，同时清除其上的作物。交互应尽可能直观。

### 4.1. `Farmland.gd` 状态与数据调整
*   在 `Farmland.gd` 中添加新属性：
    ```gdscript
    var assigned_crop_type_id: String = "" # 玩家指定的种植作物类型ID，空字符串表示未指定
    var assigned_marker: Sprite2D # 用于显示指定标记的精灵节点 (在_ready中创建和配置)
    ```
*   在 `_ready()` 中初始化 `assigned_marker`，例如加载一个小旗帜或作物图标的微缩图，并初始设置为不可见。
*   当农田因收获或清理变为空闲时，如果 `assigned_crop_type_id` 非空，农夫AI应能识别并尝试自动种植该指定作物。

### 4.2. UI交互：选择作物与指定农田
1.  **`UIManager.gd` (或类似的UI管理脚本)**:
    *   管理当前玩家在UI上选择的用于种植的作物类型ID:
        ```gdscript
        var selected_crop_for_planting: String = ""

        func select_crop_for_planting(crop_id: String) -> void:
            if crop_id == selected_crop_for_planting: // 再次点击已选中的，则取消选择
                selected_crop_for_planting = ""
                # TODO: 更新UI，移除作物选择的高亮状态
            else:
                # TODO: 检查作物是否已解锁且可种植
                # if _GameManager.data_manager.is_crop_unlocked(crop_id) and _can_afford_crop(crop_id):
                selected_crop_for_planting = crop_id
                # TODO: 更新UI，高亮选中的作物图标，可以显示一个种植光标提示
            # print("UIManager: Selected crop for planting: ", selected_crop_for_planting)
        
        func get_selected_crop_for_planting() -> String:
            return selected_crop_for_planting

        func clear_selected_crop_for_planting() -> void:
            selected_crop_for_planting = ""
            # TODO: 更新UI
        ```
2.  **UI作物图标点击**: 玩家点击UI中的某个已解锁作物图标时，调用 `UIManager.select_crop_for_planting(crop_id)`。UIManager内部处理选中状态的切换和UI反馈。
3.  **农田点击 (`Farmland.gd`)**:
    *   修改 `_on_interaction_area_input_event` (或等效的农田输入处理函数)：
        ```gdscript
        func _on_interaction_area_input_event(_viewport, event, _shape_idx) -> void:
            if event is InputEventMouseButton and event.pressed:
                var ui_manager = Utilities.get_ui_manager() # 假设有此工具方法
                if not is_instance_valid(ui_manager): return

                if event.button_index == MOUSE_BUTTON_LEFT:
                    var crop_to_plant = ui_manager.get_selected_crop_for_planting()
                    if not crop_to_plant.is_empty():
                        if can_assign_crop(): # 检查农田是否能被指定
                            assign_crop(crop_to_plant)
                            # 可选：种植后清除玩家的作物选择状态，避免连续误点
                            # ui_manager.clear_selected_crop_for_planting() 
                    else:
                        # 左键点击农田但未选择作物时的其他逻辑，如显示农田信息面板
                        show_farmland_info() 
                elif event.button_index == MOUSE_BUTTON_RIGHT:
                    handle_right_click_action()
        ```
    *   新增 `Farmland.gd` 方法:
        ```gdscript
        func can_assign_crop() -> bool: 
            # 通常允许指定，除非有特殊条件，比如农田正在进行不可中断的操作
            return true

        func assign_crop(crop_type_id: String) -> void:
            if not _GameManager.data_manager.is_crop_unlocked(crop_type_id):
                # TODO: UI提示玩家该作物未解锁
                Utilities.debug_print("尝试指定未解锁的作物: " + crop_type_id, "FARMLAND")
                return

            # 检查种植成本 (这里只指定，实际种植时再扣费，或在这里做预检查)
            # var crop_config = _GameManager.data_manager.get_crop_config(crop_type_id)
            # if _GameManager.resource_manager.get_gold() < crop_config.get("planting_cost", 999999):
            #     # TODO: UI提示金币不足
            #     return

            assigned_crop_type_id = crop_type_id
            _update_assigned_marker() # 更新指定标记的视觉
            log_message("农田 %d 指定种植 %s" % [farmland_id, crop_type_id], LogLevel.INFO)
            
            # 如果农田当前为空闲，并且有农夫可用，可以立即触发种植任务
            if is_empty() and current_state == FarmlandState.EMPTY:
                 # _GameManager.work_system.request_planting_on_farmland(self, assigned_crop_type_id)
                 inform_ai_about_new_assignment(self)


        func _update_assigned_marker() -> void:
            if not is_instance_valid(assigned_marker): return
            if not assigned_crop_type_id.is_empty():
                if not is_instance_valid(_data_manager):
                    printerr("DataManager not available in Farmland to update assigned marker.")
                    assigned_marker.texture = null // 或者一个错误图标
                    assigned_marker.visible = false
                    return

                var icon_texture: Texture2D = _data_manager.get_crop_icon_texture_for_ui(assigned_crop_type_id)
                
                if is_instance_valid(icon_texture):
                    assigned_marker.texture = icon_texture
                    assigned_marker.visible = true
                     # 可以根据图标大小调整 assigned_marker 的 scale 或其他属性
                     # assigned_marker.scale = Vector2(0.5, 0.5) # 例如，如果图标太大
                else:
                    printerr("Failed to get icon texture for assigned crop: " + assigned_crop_type_id)
                    assigned_marker.texture = load("res://assets/textures/placeholder_error.png") // 错误占位符
                    assigned_marker.visible = true
            else:
                assigned_marker.texture = null
                assigned_marker.visible = false

        func inform_ai_about_new_assignment(farmland_ref) -> void:
            # 通过信号或直接调用AI管理系统的方法，通知有新的指定种植任务
            # 例如: WorkSystem.request_specific_planting(farmland_ref, assigned_crop_type_id)
            Utilities.debug_print("农田 %s 通知AI有新种植任务: %s" % [self.name, assigned_crop_type_id], "FARMLAND")
            pass
            
        func show_farmland_info() -> void:
            # TODO: 实现显示农田详细信息的UI逻辑
            Utilities.debug_print("显示农田 %s 的信息" % self.name, "FARMLAND")
            pass

        func handle_right_click_action() -> void:
            if current_state == FarmlandState.OCCUPIED_GROWING or current_state == FarmlandState.OCCUPIED_READY_FOR_HARVEST:
                # 如果有作物（无论是否成熟），右键清理作物
                clear_crop_on_farmland(true) # 清理作物并不返还资源
            elif not assigned_crop_type_id.is_empty():
                # 如果没有作物，但有种植指定，右键取消指定
                cancel_crop_assignment()
            else:
                # 如果既没作物也没指定，右键可作为其他功能入口或无操作
                Utilities.debug_print("农田 %s 右键无明确操作" % self.name, "FARMLAND")
        ```

### 4.3. 清理农田种植指定
*   `cancel_crop_assignment()`:
    ```gdscript
    func cancel_crop_assignment() -> void:
        if not assigned_crop_type_id.is_empty():
            Utilities.debug_print("农田 %s 取消种植指定: %s" % [self.name, assigned_crop_type_id], "FARMLAND")
            assigned_crop_type_id = ""
            _update_assigned_marker()
            # 如果有AI任务指向这里因为这个指定，需要通知AI取消
            # _GameManager.work_system.cancel_task_for_farmland(self, "planting")
    ```
*   `clear_crop_on_farmland(inform_player_no_refund: bool)`:
    ```gdscript
    func clear_crop_on_farmland(inform_player_no_refund: bool) -> void:
        if is_instance_valid(current_crop):
            Utilities.debug_print("农田 %s 清理作物: %s" % [self.name, current_crop.crop_type], "FARMLAND")
            # 安全地断开与该作物实例相关的所有信号连接 (如果Crop发出了需要FarmLand监听的信号)
            # current_crop.disconnect_all_signals_from_target(self) # 假设有这样的辅助方法
            current_crop.queue_free()
            current_crop = null
        
        # 无论之前是否有作物，都取消种植指定（如果存在）
        cancel_crop_assignment() 
        
        # 重置农田状态
        _change_state(FarmlandState.EMPTY) # 会处理进度条、水分等重置
        
        if inform_player_no_refund:
            # TODO: UI提示玩家清理作物不返还资源
            _GameManager.ui_manager.show_notification("作物已清除，资源不返还。")
    ```

### 4.4. `Farmer.gd` AI与任务调整
*   **任务分配 (`FarmerTaskManager.gd` 或类似AI决策脚本)**:
    *   当农夫空闲并寻找种植任务时，应优先检查是否有 `assigned_crop_type_id` 非空的农田。
    *   如果农田 `farm.assigned_crop_type_id` 有值，且 `farm.is_empty()`，则农夫应尝试种植该作物（检查并扣除种植成本）。
*   如果玩家清除了一个正在被农夫前往种植的指定作物，农夫AI需要能优雅地处理任务中断（例如，返回空闲，寻找新任务）。

## 5. 作物视觉、生命周期与对齐优化 (Y轴排序说明简化)

### 5.1. 作物精灵图资源处理与对齐微调
*   已在 `2.3` 中通过 `AtlasTexture` 的 `region` 和 `margin` 属性得到解决。`margin` 属性允许在不修改原始SpriteSheet的情况下，为每个阶段的 `AtlasTexture` 进行像素级偏移，能完美解决不同作物或不同生长阶段视觉上的底部对齐和细微位置调整需求。

### 5.2. 作物对齐与Y轴排序 (简化说明与示例)

**采纳建议**：简化Y轴排序的说明，使其更易于初学者理解。

1.  **对齐核心**: 
    *   `Farmland.tscn` 中的 `CropPosition` (Marker2D) 定义了作物在农田上的逻辑生成点。
    *   `Crop.tscn` 的根节点 (`Crop` Node2D) 的原点 `(0,0)` 应被视为其逻辑底部和Y轴排序的参考点。
    *   `Crop.tscn` 内部的 `VisualSprite` (Sprite2D) 节点，其显示的精灵图的 *视觉底部* 应通过 `AtlasTexture` 的 `margin` 或 `VisualSprite` 节点的 `offset` 属性调整，使其对齐到 `Crop` 根节点的 `(0,0)` 位置。 （优先使用 `AtlasTexture` 的 `margin` 进行配置驱动的调整）。

2.  **Y轴排序 (YSort Node)**:
    *   **作用**: `YSort` 节点是Godot内置的一个特殊2D节点。当你把其他2D节点（如Sprite2D, CharacterBody2D, Node2D实例等）作为 `YSort` 节点的子节点时，`YSort` 会自动根据这些子节点各自的 **Y坐标**（它们原点的Y值）来决定它们的绘制顺序。简单来说，Y值越大的子节点会被画在Y值小的子节点的前面，产生近大远小或上下遮挡的景深效果。
    *   **场景结构示例**:
        ```gdscript
        - GameWorld (Node2D) # 游戏世界根节点, YSort也可以放在这里如果所有东西都需排序
          - BackgroundLayer (Node2D) # 地图瓦片、纯背景图等，不需要YSort
            - FarmlandTile1_Sprite, FarmlandTile2_Sprite ...
          - GameplayLayer (YSort) # <--- YSort节点作用于此层及其所有子节点
            - FarmerCharacter (CharacterBody2D) # 农夫实例，其Y坐标是脚底位置
            - CropInstance_Farm1 (Node2D)     # 作物场景实例，其Y坐标是逻辑底部
            - CropInstance_Farm2 (Node2D)
            - TreeObject (StaticBody2D)       # 树木等障碍物，Y坐标是根部
            - DroppedItem_Sprite (Sprite2D)   # 地上的掉落物
            - ... 其他所有需要根据Y值动态排序的动态对象 ...
        ```
    *   **如何工作**: 将农夫实例、所有作物实例 (`Crop`场景的实例)、树木、地上的掉落物等放入 `GameplayLayer` (即YSort节点) 下。Godot 会自动处理它们的渲染顺序。你只需要确保这些对象的原点 (通常是其各自场景根节点的 `(0,0)` 位置) 代表了它们在游戏世界中的"脚底"或Y轴排序的基准点。
    *   **注意**: `Farmland` 节点本身（特别是其地块Sprite）通常不需要参与YSort，它们是背景的一部分。只有其上种植的动态 `Crop` 实例需要YSort。因此，当 `Farmland.gd` 实例化一个 `Crop` 时，应该 `add_child(crop_instance)` 到这个公共的 `GameplayLayer`(YSort) 节点，并正确设置其 `global_position`。

### 5.3. 作物完整生命周期（从种植到存储）
作物的生命周期将严格按照以下流程执行，确保与新的 `AtlasTexture` 视觉更新方式、玩家指定的种植流程以及农田清理机制兼容：

1.  **指定与种植**:
    *   玩家通过UI选择目标作物。
    *   玩家点击农田，触发 `Farmland.assign_crop(selected_crop_id)`，农田记录下被指定的作物类型。
    *   如果此时有空闲农夫且该农田为空，农夫AI系统（例如 `WorkSystem` 或 `FarmerTaskManager`）会识别到这个新的指定种植任务。
    *   农夫移动到目标农田。
    *   农夫播放种植动画。
    *   农夫调用 `Farmland.plant_crop(assigned_crop_type_id)`。此过程会检查并扣除种植成本。

2.  **`Farmland.plant_crop(crop_id_to_plant)`**:
    *   从 `DataManager` 获取 `crop_id_to_plant` 的详细配置。
    *   实例化 `Crop.tscn` 场景，得到 `crop_instance`。
    *   设置 `crop_instance.global_position` 为农田上 `CropPosition` (Marker2D) 的全局位置。
    *   将 `crop_instance` 添加为 `GameplayLayer` (YSort节点) 的子节点，以确保正确的渲染排序。
    *   调用 `crop_instance.initialize(crop_id_to_plant, crop_config)`，传递作物ID和配置数据。
    *   调用 `crop_instance.start_growing()`，启动生长计时器和水分消耗逻辑。
    *   农田状态更新为 `FarmlandState.OCCUPIED_GROWING`。
    *   `current_crop` 引用指向 `crop_instance`。

3.  **`Crop.initialize(crop_id, config)` 和 `Crop.start_growing()`**:
    *   保存 `crop_id` 和 `config`。
    *   根据配置中的 `visuals` 信息，调用 `_load_main_texture()` 加载作物的SpriteSheet。
    *   设置初始视觉状态为 "seed" 阶段 (调用 `_update_appearance()`)。
    *   初始化生长进度 `growth_progress = 0.0` 和生长阶段 `growth_stage`。
    *   启动内部计时器或在 `_process` 中开始累积生长时间。
    *   开始根据配置消耗水分 (通过与 `Farmland` 交互或自身逻辑)。

4.  **`Crop._process(delta)`**:
    *   根据 `delta` 和可能的生长修正因子（来自水分、天气等）更新 `growth_progress`。
    *   调用 `_update_appearance()` 以根据 `growth_progress` 更新作物的视觉表现 (切换 `AtlasTexture`)。
    *   检查水分是否充足，并可能调整生长速率。
    *   当 `growth_progress >= 1.0` 时，调用 `_on_growth_completed()`。

5.  **`Crop._on_growth_completed()`**:
    *   标记作物为成熟状态 (例如 `is_mature = true`)。
    *   发射 `growth_completed` 信号，参数为 `self` (作物实例)。

6.  **`Farmland._on_crop_growth_completed(crop_instance)`**:
    *   农田状态更新为 `FarmlandState.OCCUPIED_READY_FOR_HARVEST`。
    *   发射 `ready_for_harvest` 信号，参数为 `self` (农田实例) 和 `crop_instance`。

7.  **收获**:
    *   农夫AI识别到 `ready_for_harvest` 的农田，并接受收获任务。
    *   农夫移动到农田。
    *   农夫播放收获动画。
    *   农夫调用 `Farmland.harvest()`。

8.  **`Farmland.harvest()`**:
    *   如果 `current_crop` 有效且已成熟，则调用 `current_crop.harvest()`。
    *   农田状态变回 `FarmlandState.EMPTY` (如果 `assigned_crop_type_id` 为空) 或准备重新种植指定作物。

9.  **`Crop.harvest()`**:
    *   根据配置中的 `yield_amount`（例如 `{"min": 1, "max": 3}`）随机计算本次的收获数量。
    *   发射 `harvested` 信号，参数为 `self` (作物实例), `crop_type` (作物ID), `calculated_yield_amount`。
    *   调用 `queue_free()` 销毁作物实例。

10. **农夫携带与存储**:
    *   农夫脚本（`Farmer.gd`）监听到 `harvested` 信号或通过任务系统得知收获成功和具体产物。
    *   农夫内部状态更新，例如 `set_carrying_resource(crop_type, calculated_yield_amount)`。
    *   农夫视觉更新，显示其正在携带收获的作物（例如，头顶出现作物图标）。
    *   农夫AI规划路径并移动到指定的存储建筑（例如仓库 `StorageBuilding`）。
    *   到达存储建筑后，与建筑交互，调用存储建筑的接口（例如 `StorageBuilding.store_resource(crop_type, calculated_yield_amount)`）。
    *   资源成功存入后，农夫调用 `clear_carrying_resource()` 清除携带状态和视觉。
    *   农夫变为空闲状态，寻找下一个任务。

## 6. 农夫角色集成与系统稳定性
农夫系统与新的种植机制的集成需要细致处理，确保整体稳定性和良好的玩家体验。关键点包括：
*   **任务系统的健壮性**：农夫的AI和任务分配逻辑（无论是在 `Farmer.gd` 内部还是通过外部的 `WorkSystem` 或 `FarmerTaskManager`）必须能够灵活响应农田状态的变化。
    *   **任务优先级**：明确农夫执行任务的优先级，例如：优先处理玩家指定的种植/清理任务，然后是成熟作物的收获，其次是浇水，最后是常规的空闲农田种植。
    *   **任务中断处理**：如果一个农夫正在前往某个农田执行任务（例如，去种植一个指定的作物A），而此时玩家更改了该农田的指定（例如，改成了作物B或清除了指定），农夫AI应能优雅地取消当前任务，返回空闲状态，并重新评估是否有新任务可做。避免出现农夫继续执行一个已失效任务的情况。
    *   **资源检查**：农夫在执行种植任务前，必须检查玩家是否有足够的种植成本（例如金币），如果不足，则任务失败，农田的指定状态保留，农夫返回空闲。
*   **信号连接管理**：
    *   所有通过信号连接的交互（例如 `Crop` -> `Farmland`，`Farmland` -> `Farmer/WorkSystem`）必须确保在对象被释放（`queue_free()`）前断开连接，以防调用到无效对象。
    *   使用 `is_instance_valid()` 在信号回调或直接方法调用前检查对象实例的有效性。
*   **动画与状态同步**：农夫的动画（行走、种植、浇水、收获）应与其当前的具体任务和状态严格同步。
*   **测试覆盖**：
    *   针对各种边缘情况进行充分测试：玩家快速连续指定/清除农田，农夫在移动过程中目标作物被移除，资源不足无法种植等。
    *   测试多农夫协同工作时的表现，确保任务分配的合理性和无冲突。
*   **错误反馈与日志**：在关键的决策点或错误发生时，使用 `Utilities.debug_print()` 或 `printerr()` 输出清晰的日志，便于调试。当发生可预见的玩家操作错误（如金币不足无法种植）时，应有明确的UI反馈给玩家。

通过上述措施，旨在建立一个农夫能够智能、稳定地与动态变化的种植系统互动的框架。

## 7. 水分系统深化与玩法结合 (重点更新)

### 7.1. 农夫浇水能力与升级
为了使农夫的浇水行为更具策略性和成长性，我们将实现以下机制：
*   **基础浇水能力**: 在 `Farmer.gd` (或其专门的能力组件脚本，如果您的农夫属性复杂) 中定义农夫单次浇水动作提供的基础水分量。
    ```gdscript
    # In Farmer.gd or FarmerStatsComponent.gd
    var base_water_amount_per_action: float = 10.0 # 农夫一次浇水可以为农田增加10点水分
    ```
*   **技能升级系统 (可选但推荐)**: 引入农夫的"浇水技能等级"或通过升级工具（如水桶）来提升浇水效率。
    ```gdscript
    var watering_skill_level: int = 1 # 初始等级为1
    const WATERING_EFFICIENCY_PER_LEVEL: float = 0.2 # 每升一级，浇水效率提升20%
    
    func get_effective_watering_amount() -> float:
        # 计算公式可以调整，例如：
        # 线性增长: base_water_amount_per_action * (1 + (watering_skill_level - 1) * WATERING_EFFICIENCY_PER_LEVEL)
        # 或指数增长，或基于特定道具的加成
        var efficiency_multiplier = 1.0 + (watering_skill_level - 1) * WATERING_EFFICIENCY_PER_LEVEL
        return base_water_amount_per_action * efficiency_multiplier
    ```
*   **交互实现**: 当农夫AI决定对一个农田执行浇水任务时：
    1.  农夫移动到农田。
    2.  播放浇水动画。
    3.  调用目标农田的 `water(amount: float)` 方法，传递其 `get_effective_watering_amount()` 的计算结果。
        ```gdscript
        # In Farmer.gd, when performing watering action on a target_farmland
        if is_instance_valid(target_farmland):
            var water_to_add = get_effective_watering_amount()
            target_farmland.water(water_to_add)
        ```
*   **升级途径**: 浇水技能的提升可以与农夫的等级、特定研究项目或购买/制作更高级的浇水工具（如"铜水桶"，"铁水桶"）相关联。

### 7.2. 农田水分消耗与作物生长影响
农田的水分将动态变化，并直接影响作物的生长速度。此机制让水分管理成为持续关注点，缺水会惩罚作物生长，鼓励玩家或农夫及时浇水。

*   **作物配置驱动**:
    *   在 `crops.json` 的每种作物配置中，明确定义其水分需求：
        *   `water_consumption_per_second`: (Float) 该作物在正常情况下每秒消耗多少水分。
        *   `optimal_water_threshold_percentage`: (Float, 范围0.0-1.0) 农田的当前水分百分比（`water_level / water_capacity`）低于此阈值时，作物的生长速度将开始受到负面影响。例如，0.3 表示水分低于30%时生长减缓。
        *   `growth_penalty_multiplier_if_dry`: (Float, 0.0-1.0, e.g., 0.8) 当水分不足时，生长速度乘以该系数。
*   **`Farmland.gd` 中的水分管理**:
    *   在 `_process(delta)` 方法中：
        *   **基础水分蒸发** (可选): 可以加入一个缓慢的自然蒸发逻辑。
            ```gdscript
            # var base_evaporation_rate = 0.05 # 每秒蒸发0.05点水
            # water_level = max(0, water_level - base_evaporation_rate * delta)
            ```
        *   如果农田上有作物 (`current_crop != null` 且有效) 并且作物正在生长：
            *   获取当前作物的 `water_consumption_per_second` 配置。
            *   计算本帧水分消耗量：`consumed_water = current_crop.get_config_value("water_consumption_per_second") * delta`。
            *   减少农田水分：`water_level = max(0, water_level - consumed_water)`。
            *   调用 `_update_farmland_visuals()` 更新农田干湿视觉。
            *   调用 `_update_water_display()` 更新水分UI。
            *   计算当前水分百分比：`current_water_percentage = water_level / water_capacity` (注意避免除零)。
            *   获取当前作物的 `optimal_water_threshold_percentage` 和 `growth_penalty_multiplier_if_dry` 配置。
            *   根据比较结果，确定一个生长修正系数 `growth_modifier`：
                ```gdscript
                var growth_modifier: float = 1.0 # 默认正常生长
                if current_water_percentage < current_crop.get_config_value("optimal_water_threshold_percentage"):
                    growth_modifier = current_crop.get_config_value("growth_penalty_multiplier_if_dry", 0.8)
                
                # 将修正系数传递给作物
                if is_instance_valid(current_crop) and current_crop.has_method("set_external_growth_modifier"):
                    current_crop.set_external_growth_modifier(growth_modifier)
                ```
*   **`Crop.gd` 中的生长速率调整**:
    *   添加一个方法 `set_external_growth_modifier(modifier: float)` 用于接收来自农田的生长修正。
        ```gdscript
        # In Crop.gd
        var _external_growth_modifier: float = 1.0

        func set_external_growth_modifier(modifier: float) -> void:
            _external_growth_modifier = clamp(modifier, 0.0, 2.0) # 限制修正范围
        ```
    *   在 `Crop.gd` 的 `_process(delta)` 中，计算有效生长增量时应用此修正：
        ```gdscript
        # In Crop.gd, _process(delta)
        if is_growing and not is_mature:
            var base_growth_per_second = (1.0 / get_config_value("growth_time_seconds"))
            # _internal_growth_modifier 可以是作物自身的特性，如"耐旱"特性带来的修正
            var effective_growth_rate = base_growth_per_second * _internal_growth_modifier * _external_growth_modifier 
            growth_progress += effective_growth_rate * delta
            growth_progress = clamp(growth_progress, 0.0, 1.0)
            # ... (调用 _update_appearance(), 检查成熟等) ...
        ```

### 7.3. 与天气/季节系统联动 (新)
**(此部分功能已暂缓)**

**说明**：原计划中包含天气/季节系统与水分管理、作物生长的联动。考虑到当前开发阶段的复杂度和工作量，此高级功能将**延后至核心种植系统稳定后再行考虑和设计**。目前，水分系统将独立运作，不受外部天气/季节因素影响。

## 8. 健壮性与错误处理 (重点更新)

### 8.1. 资源加载失败处理
*   **纹理/SpriteSheet加载 (`Crop.gd` 的 `_load_main_texture` 及类似地方)**: 
    *   在使用 `load(path)` 前，务必先用 `ResourceLoader.exists(path)` 检查路径有效性。
    *   如果加载失败 (路径无效或 `load()` 返回 `null`)：
        1.  `printerr()` 打印详细错误信息，包括期望的资源路径、作物ID等，方便调试。
        2.  将目标纹理（如 `_main_crop_texture` 或 `visual_sprite.texture`）设置为一个预先准备好的"**占位符/错误提示**"纹理 (例如 `res://assets/textures/placeholder_error.png`，可以是一个红色的问号或"Error"字样)。
        3.  相关对象的初始化状态应标记为失败或部分失败（例如 `_is_visuals_initialized = false`），其行为应作相应降级（例如，该作物在UI上显示错误，农夫无法种植或收获此"损坏"作物）。
*   **图标资源加载**：同理，UI显示或农夫携带图标时，若图标资源加载失败，也应显示占位符图标。

### 8.2. 配置缺失处理 (`DataManager.gd`)
*   当 `DataManager.get_crop_config(crop_type_id)` 时：
    1.  如果 `crops.json` 中找不到 `crop_type_id` 对应的作物配置条目：
        *   `printerr()` 错误信息。
        *   返回一个空的 `Dictionary` 或一个特殊的 `null` / 错误标记对象。
    2.  如果找到了作物条目，但其中缺少关键属性（例如 `visuals`, `growth_time_seconds`, `yield_amount` 等）：
        *   `printerr()` 指出缺失的具体属性。
        *   可以考虑为这些关键属性提供一个"安全默认值"（例如，生长时间设为极大值，产量为0，使其在游戏中"无用"但不会崩溃），或者同样返回错误标记。
*   **调用方处理**: 获取配置的代码（如 `Crop.gd` 的 `initialize`，`Farmland.gd` 的 `plant_crop`)必须检查返回的配置是否有效/完整。如果配置无效：
    *   阻止该作物的实例化或相关操作。
    *   UI上应将该作物标记为不可用或显示错误状态。

### 8.3. 空引用检查 (所有脚本)
*   **`@onready var` 引用**: 在 `_ready()` 函数或首次使用前，通过 `if not is_instance_valid(node_ref): printerr("Node %s not found!"); return` 或类似方式检查通过路径获取的节点是否真的存在。
*   **方法调用前**: 对于任何可能为 `null` 或已从场景树移除的节点变量，在调用其方法或访问其属性前，务必使用 `if is_instance_valid(node_ref): node_ref.some_method()` 进行检查。
*   **信号连接对象**: 在连接信号时，确保发射信号的对象和接收信号的对象都有效。在对象 `queue_free()` 之前，应负责断开其发出的或连接到它的信号，以避免调用已释放对象的错误。

通过这些措施，可以显著提高游戏在遇到资源或配置问题时的容错能力，便于开发者定位问题，并避免因这些问题导致游戏崩溃，从而优化玩家体验。

## 9. 代码规模与脚本管理
此种植系统的实现，包括作物视觉、生命周期、玩家指定、水分管理、天气联动以及农夫AI的相应调整，预计会使相关脚本（主要是 `Farmland.gd`, `Crop.gd`, `Farmer.gd`，以及可能的 `WeatherManager.gd` 和 `DataManager.gd`）的代码行数有所增加。

*   `Farmland.gd`: 预计增加 100-150行 (处理指定作物、水分消耗、天气响应、视觉更新)。
*   `Crop.gd`: 预计增加 150-200行 (AtlasTexture处理、生长逻辑、配置驱动、与农田交互)。
*   `Farmer.gd`: 预计增加 80-120行 (任务逻辑调整、浇水能力、与新农田状态交互)。
*   `WeatherManager.gd` (如果新建): 预计 50-80行。
*   `DataManager.gd`: 查找作物配置和解锁条件的逻辑会略微增加复杂度，约30-50行。

总体增加的行数在可控范围内。我们将持续关注每个脚本的规模和职责清晰度。如果任何一个脚本因为功能集成而显得过于臃肿（例如，预估持续增长并显著超过800-1000行），或者其承担的职责过多，我们会主动提出拆分建议。可能的拆分点包括：
*   将农夫的复杂AI决策逻辑（如任务选择、路径规划）拆分到专门的 `FarmerAIBehavior.gd` 或任务管理类。
*   如果作物解锁条件变得非常复杂多样，可以考虑一个 `CropUnlockValidator.gd` 辅助类。
*   天气系统的具体效果计算和状态管理可以更细致地封装在 `WeatherManager.gd` 内部。

目标是保持代码模块化、高内聚、低耦合，便于维护和未来扩展，同时遵循您对脚本行数的偏好。

## 10. Godot 4.3 最佳实践遵循
在整个开发过程中，我们将严格遵守Godot 4.3的开发规范和最佳实践，确保代码质量和项目稳定性。具体包括：
*   **静态类型**: 所有变量声明、函数参数和返回类型都将使用GDScript的静态类型系统（例如 `var health: int = 100`，`func deal_damage(amount: int) -> int:`）。这有助于在编码阶段捕捉类型错误，并提高代码的可读性和IDE的智能提示能力。
*   **`await` 替代 `yield`**: 对于所有异步操作和需要等待的场景（例如计时器、信号），将使用Godot 4引入的 `await` 关键字，而不是旧版的 `yield`。例如：`await get_tree().create_timer(1.0).timeout` 或 `await some_node.some_signal_name`。
*   **推荐的信号处理方式**:
    *   **定义**: 使用 `signal signal_name(param1: Type, param2: Type)` 的方式清晰定义信号及其参数。
    *   **连接**: 使用 `emitter_node.signal_name.connect(Callable(receiver_node, "method_name"))` 或简写 `emitter_node.signal_name.connect(receiver_node.method_name)` (如果方法明确且无歧义) 进行连接。优先使用 `Callable` 以增强类型安全和重构支持。
    *   **发射**: 使用 `signal_name.emit(arg1, arg2)` 的方式发射信号，而不是旧版的 `emit_signal("signal_name", arg1, arg2)`。
    *   **检查与断开**: 在适当的时候（例如对象销毁前 `_exit_tree()`）使用 `is_connected()` 和 `disconnect()` 管理信号连接，防止内存泄漏或调用无效对象。
*   **正确的资源API**:
    *   **加载**: 使用 `load("res://path/to/resource")` 加载编译时已知的资源，使用 `ResourceLoader.load("res://path/to/resource")` 或 `ResourceLoader.load_threaded_request()` / `get()` / `has()` 系列API进行运行时动态加载，并配合 `ResourceLoader.exists(path)` 进行检查。
    *   **预加载**: 对性能关键且必定会用到的资源，使用 `preload("res://path/to/resource")` 在脚本编译时加载。
    *   **错误处理**: 检查 `load()` 或 `ResourceLoader.load()` 的返回值是否为 `null`，以处理资源加载失败的情况。
*   **场景实例化**: 使用 `packed_scene.instantiate()` 方法创建场景实例。
*   **节点引用**: 优先使用 `@onready var node_name = $Path/To/Node` 获取场景树中的节点引用，对于运行时动态添加或不确定的节点，使用 `get_node_or_null()` 并进行有效性检查 (`is_instance_valid()`)。
*   **代码风格**: 遵循项目已定义的命名规范（PascalCase类名，snake_case变量/函数名等）、缩进（4空格）、行长度限制等编码风格指南。
*   **避免硬编码路径和魔法数字**: 尽可能将配置数据（如资源路径、数值参数）存放在配置文件（如JSON）或导出变量中，而不是硬编码在脚本里。使用常量（`const`）或枚举（`enum`）代替魔法数字和字符串。

遵循这些实践有助于构建一个更健壮、可维护和易于团队协作的Godot项目。

## 11. 玩法循环考量 (新)

**采纳建议**：明确各项机制如何服务于核心玩法循环，提升游戏体验的整体性和目标感。
**(注：因天气/季节系统暂缓，相关影响已从此部分移除或弱化)**

*   **短期核心循环 (种植 -> 等待 -> 交互 -> 收获 -> 决策)**:
    *   **指定种植功能**：让玩家对农田布局和产出有更直接的控制，服务于"决策"环节，可为特定目标（如任务、升级）定向生产。
    *   **水分系统**：为"等待"和"交互"（浇水）环节增加了动态变量和管理维度。玩家需关注作物状态，及时作出反应，获得即时反馈。高阶作物对水分的依赖性增强，提升管理需求。
    *   **视觉与对齐优化**：提升"收获"和整体观察的满足感和清晰度。
*   **中期成长循环 (积累 -> 解锁 -> 拓展 -> 优化)**:
    *   **作物解锁机制与平滑且有深度的成长曲线**：构成了中期游戏的核心驱动力。玩家通过"积累"资源（金钱、特定作物）来"解锁"新作物或更高级的农夫能力（如浇水效率），从而"拓展"种植规模和种类，并"优化"生产效率。**长周期、高投入的高阶作物设计，为此循环提供了长远目标。**
    *   **属性设计**：新作物带来的更高收益或特殊产出，激励玩家持续投入和探索。
*   **长期放置循环 (规划 -> 投入 -> 巨大回报/稀有成就 -> 新挑战)**:
    *   **高阶作物的长周期与高回报设计**：满足放置类玩家对长期目标和可观收益的期待。"投入"大量时间和资源后获得"巨大回报"。这些作物的存在本身就是一种"新挑战"。
    *   **复杂的解锁条件（如建筑、研究）**：为后期游戏提供持续的策略深度。玩家需要进行更宏观的"规划"。
    *   **预留的Buff空间**：即使不立即实现复杂的Buff系统，属性设计中为Buff预留空间（例如，基础生长时间较长）本身就暗示了未来的优化路径，为长期玩家提供持续优化的可能性，保持游戏活力。
*   **健壮性与错误处理的隐性贡献**: 虽然不直接体现在玩法机制上，但一个稳定、不易出错的系统是所有良好玩法体验的基础。清晰的错误提示帮助开发者快速迭代，避免了因bug导致的挫败感，间接保障了玩家能顺畅体验上述循环。

这些机制的设计旨在相互关联，共同构建一个从简单上手到具有深度管理和长期追求的村庄模拟种植体验。

## 12. 总结与后续步骤

这份经过您建议强化的优化方案，现在更加全面和细致。我们重点采纳了单一`Sprite2D`+`AtlasTexture`的作物视觉方案，大幅调整并深化了作物成长曲线以适应长周期放置特性，优化了玩家指定种植的交互流程，并明确了水分系统对作物生长的影响。同时，根据您的指示，**已暂缓天气/季节系统的联动功能**。方案依然强调系统的健壮性和错误处理。

**后续步骤建议**：
**(此处的具体步骤已被提升并整合到文档开头的"二、详细实施步骤计划"中，此处保留一个简要总结即可，或直接说明步骤已前置)**
本文档开头的 **"二、详细实施步骤计划"** 已列出了详细的执行步骤。请Boss参照该计划，我们可以从步骤1开始推进。

Boss，请您审阅这份最终版方案。如果这次的内容和结构没有问题，我已准备好根据您的指示，从我们商定的第一个具体实施点开始工作！
