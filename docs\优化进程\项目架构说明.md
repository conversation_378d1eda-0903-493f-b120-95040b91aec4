scripts/
├── 🎮 core/                    # 核心系统 (8个文件) ✅ 职责明确
│   ├── GameManager.gd          # 全局协调器
│   ├── DataManager.gd          # 数据管理
│   ├── InputManager.gd         # 输入处理
│   ├── TerrainManager.gd       # 地形管理
│   ├── SceneSwitcher.gd        # 场景切换
│   └── WindowManager.gd        # 窗口管理
│
├── 🏢 managers/               # 全局管理器 (5个文件) ✅ 职责清晰
│   ├── BuildingManager.gd      # 全局建筑管理
│   ├── CharacterManager.gd     # 全局角色管理
│   ├── ResourceManager.gd      # 全局资源管理
│   ├── GridAreaManager.gd      # 全局网格管理
│   └── UIManager.gd           # 全局UI管理
│
├── 🔧 systems/                # 系统层 (3个文件) ✅ 功能完整
│   ├── SystemsManager.gd       # 系统协调器
│   ├── AttributeBoostSystem.gd # 属性增强系统
│   └── EquipmentManager.gd     # 🆕 装备管理系统
│
├── 🎣 fishing/                # 渔业系统 ✅ 结构完整
│   └── managers/
│       ├── FishingDataManager.gd    # 🆕 渔业数据管理
│       └── FishingTaskManager.gd    # 渔业任务管理
│
└── ... (其他系统保持不变)


窗口管理架构
├── 🖥️ GameManager (系统级)
│   ├── 窗口位置设置
│   ├── 屏幕适配
│   └── 系统窗口属性
│
├── 🎮 UIManager (游戏级)
│   ├── UI组件管理
│   ├── 面板显示/隐藏
│   └── 建筑放置模式
│
└── 🎨 MainUI (界面级)
    ├── 标签页切换
    ├── 信号处理
    └── 角色管理界面