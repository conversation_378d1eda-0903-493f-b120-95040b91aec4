# MineInteractionTest.gd
# 测试脚本：验证矿工与矿井建筑的交互功能
extends Node

# 统一状态系统
const UnifiedStates = preload("res://scripts/core/UnifiedCharacterStates.gd")

## 测试配置 ##
var test_miner: Miner = null
var test_mine: MineBuilding = null
var test_tramcar: TramcarBuilding = null
var world: Node = null

## 测试方法 ##
func _ready() -> void:
    print("\n=== 矿工建筑交互测试开始 ===")
    
    # 延迟运行测试，确保场景加载完成
    call_deferred("run_tests")

func run_tests() -> void:
    """运行所有测试"""
    if not _setup_test_environment():
        print("测试环境设置失败")
        return
    
    print("\n--- 测试环境设置完成 ---")
    print("矿工: %s" % test_miner.name if test_miner else "未找到")
    print("矿井: %s" % test_mine.name if test_mine else "未找到")
    print("矿车: %s" % test_tramcar.name if test_tramcar else "未找到")
    
    # 测试序列
    _test_mine_building_functionality()
    _test_tramcar_functionality()
    _test_miner_mine_interaction()
    
    print("\n=== 矿工建筑交互测试完成 ===\n")

func _setup_test_environment() -> bool:
    """设置测试环境"""
    # 查找世界节点
    world = get_tree().get_first_node_in_group("world")
    if not world:
        print("错误：未找到世界节点")
        return false
    
    # 查找矿工（如果存在）
    var miners = get_tree().get_nodes_in_group("miners")
    if miners.size() > 0:
        test_miner = miners[0] as Miner
    
    # 查找矿井建筑
    var mines = get_tree().get_nodes_in_group("mines")
    if mines.size() > 0:
        test_mine = mines[0] as MineBuilding
    
    # 查找矿车建筑
    var tramcars = get_tree().get_nodes_in_group("tramcars")
    if tramcars.size() > 0:
        test_tramcar = tramcars[0] as TramcarBuilding
    
    return test_mine != null and test_tramcar != null

func _test_mine_building_functionality() -> void:
    """测试矿井建筑功能"""
    print("\n--- 测试矿井建筑功能 ---")
    
    if not test_mine:
        print("跳过矿井测试：矿井建筑未找到")
        return
    
    # 测试基本属性
    print("矿井最大矿工数: %d" % test_mine.max_miners)
    print("矿井入口状态: %s" % ("激活" if test_mine.entrance_active else "关闭"))
    print("当前矿工数: %d" % test_mine.get_current_miner_count())
    
    # 测试交互数据
    var interaction_data = test_mine.get_interaction_data("right", "enter_mine")
    print("进入矿井交互数据: %s" % interaction_data)
    
    # 测试can_interact_with
    print("可以进入矿井: %s" % test_mine.can_interact_with("enter_mine", null))
    print("可以检查矿井: %s" % test_mine.can_interact_with("inspect", null))
    
    # 测试状态获取
    var mine_status = test_mine.get_mine_status()
    print("矿井状态: %s" % mine_status)

func _test_tramcar_functionality() -> void:
    """测试矿车建筑功能"""
    print("\n--- 测试矿车建筑功能 ---")
    
    if not test_tramcar:
        print("跳过矿车测试：矿车建筑未找到")
        return
    
    # 测试基本属性
    print("矿车最大容量: %d" % test_tramcar.max_storage_capacity)
    print("当前总存储: %d" % test_tramcar.get_total_stored())
    print("可用存储空间: %d" % test_tramcar.get_available_storage_space())
    print("支持的资源类型: %s" % test_tramcar.storage_types)
    
    # 测试交互数据
    var interaction_data = test_tramcar.get_interaction_data("right", "store")
    print("存储交互数据: %s" % interaction_data)
    
    # 测试can_interact_with
    print("可以存储物品: %s" % test_tramcar.can_interact_with("store", null))
    print("可以检查矿车: %s" % test_tramcar.can_interact_with("inspect", null))
    
    # 测试存储状态
    var storage_status = test_tramcar.get_storage_status()
    print("矿车存储状态: %s" % storage_status)

func _test_miner_mine_interaction() -> void:
    """测试矿工与矿井的交互"""
    print("\n--- 测试矿工与矿井交互 ---")
    
    if not test_miner or not test_mine:
        print("跳过交互测试：缺少矿工或矿井")
        return
    
    # 检查矿工初始状态
    print("矿工位置: (%f, %f)" % [test_miner.global_position.x, test_miner.global_position.y])
    print("矿工是否在地下: %s" % test_miner.is_underground)
    print("矿工当前状态: %s" % UnifiedStates.get_state_name(test_miner.get_unified_state()))
    
    # 测试距离计算
    var mine_pos = test_mine.global_position
    var distance = test_miner.global_position.distance_to(mine_pos)
    print("矿工到矿井距离: %f" % distance)
    
    # 如果矿工离矿井很近，可以尝试交互测试
    if distance < 100:
        print("尝试矿井交互测试...")
        var interaction_success = test_mine.interact(test_miner, "inspect", {})
        print("检查矿井交互结果: %s" % interaction_success)
    else:
        print("矿工距离矿井太远，跳过交互测试")

## 辅助方法 ##
func print_separator(title: String) -> void:
    """打印分隔符"""
    print("\n" + "=".repeat(50))
    print("  " + title)
    print("=".repeat(50) + "\n") 