# EquipmentManager.gd
# 装备管理器 - 配置驱动的装备系统核心
# 📁 位置: scripts/systems/EquipmentManager.gd
class_name EquipmentManager
extends Node

## 信号定义 ##
signal equipment_config_loaded()
signal equipment_equipped(character_id: String, slot: String, item_id: String)
signal equipment_unequipped(character_id: String, slot: String, item_id: String)
signal equipment_effects_changed(character_id: String, total_effects: Dictionary)

## 配置驱动常量 ##
const CONFIG_PATH = GameConstants.EquipmentConstants.EQUIPMENT_CONFIG_PATH
const EQUIPMENT_SLOTS = GameConstants.EquipmentConstants.EQUIPMENT_SLOTS
const CHARACTER_TYPES = GameConstants.EquipmentConstants.CHARACTER_TYPES
const EFFECT_MAPPING = GameConstants.EquipmentConstants.EFFECT_TO_ATTRIBUTE_MAPPING
const EFFECT_TYPES = GameConstants.EquipmentConstants.EFFECT_PROCESS_TYPES

## 核心数据存储 ##
var equipment_config: Dictionary = {}
var character_equipment: Dictionary = {}  # character_id -> {slot -> item_id}
var character_effects_cache: Dictionary = {}  # character_id -> total_effects
var character_types: Dictionary = {}  # character_id -> character_type

## 依赖管理器 ##
var data_manager = null
var resource_manager = null

## 状态标志 ##
var _is_loaded: bool = false

## 生命周期方法 ##
func _ready() -> void:
    """初始化装备管理器 - 配置驱动架构"""
    # 初始化依赖管理器
    _initialize_dependencies()

    # 加载装备配置
    _load_equipment_config()

func _initialize_dependencies() -> void:
    """初始化依赖管理器 - 标准模式"""
    var game_manager = _get_game_manager_reference()
    if not game_manager:
        push_warning("EquipmentManager: GameManager未找到，使用回退模式")
        return

    # 获取数据管理器
    if game_manager.has_method("get_data_manager"):
        data_manager = game_manager.get_data_manager()

    # 获取资源管理器
    if game_manager.has_method("get_resource_manager"):
        resource_manager = game_manager.get_resource_manager()

func _get_game_manager_reference():
    """获取GameManager引用 - 标准模式"""
    var game_manager = get_node_or_null("/root/_GameManager")
    if is_instance_valid(game_manager):
        return game_manager
    return null

## 配置加载 - 配置驱动架构 ##
func _load_equipment_config() -> bool:
    """加载装备配置 - 配置驱动模式"""
    # 尝试从DataManager获取配置
    if is_instance_valid(data_manager) and data_manager.is_initialized:
        var config = data_manager.get_equipment_config()
        if not config.is_empty():
            equipment_config = config
            _is_loaded = true
            equipment_config_loaded.emit()
            return true

    # 回退：直接加载配置文件
    return _load_config_from_file()

func _load_config_from_file() -> bool:
    """从文件加载配置 - 回退机制"""
    if not FileAccess.file_exists(CONFIG_PATH):
        push_error("EquipmentManager: 配置文件不存在: %s" % CONFIG_PATH)
        return false

    var file = FileAccess.open(CONFIG_PATH, FileAccess.READ)
    if not file:
        push_error("EquipmentManager: 无法打开配置文件")
        return false

    var json_text = file.get_as_text()
    file.close()

    var json = JSON.new()
    var parse_result = json.parse(json_text)

    if parse_result != OK:
        push_error("EquipmentManager: 解析配置失败，行: %d" % json.error_line)
        return false

    equipment_config = json.data
    _is_loaded = true
    equipment_config_loaded.emit()
    return true

## 角色装备管理 ##
func register_character(character_id: String, character_type: String = "") -> void:
    """注册角色到装备系统"""
    if character_id.is_empty():
        return
    
    if not character_equipment.has(character_id):
        character_equipment[character_id] = {}
        character_types[character_id] = character_type
        
        # 初始化所有装备槽位为空
        for slot in EQUIPMENT_SLOTS:
            character_equipment[character_id][slot] = ""
        
        print("EquipmentManager: 角色 %s 已注册到装备系统" % character_id)

func unregister_character(character_id: String) -> void:
    """从装备系统注销角色"""
    if character_equipment.has(character_id):
        character_equipment.erase(character_id)
        character_effects_cache.erase(character_id)
        character_types.erase(character_id)
        print("EquipmentManager: 角色 %s 已从装备系统注销" % character_id)

func equip_item(character_id: String, slot: String, item_id: String) -> bool:
    """装备物品 - 统一接口"""
    # 基础验证
    if not _validate_equip_request(character_id, slot, item_id):
        return false

    # 获取当前装备状态
    var char_equipment = get_character_equipment(character_id)
    var old_item_id = char_equipment.get(slot, "")

    # 如果装备相同，跳过
    if old_item_id == item_id:
        return true

    # 执行装备变更
    return _execute_equipment_change(character_id, slot, old_item_id, item_id)

func _validate_equip_request(character_id: String, slot: String, item_id: String) -> bool:
    """验证装备请求"""
    # 系统状态检查
    if not _is_loaded:
        return false

    # 参数验证
    if character_id.is_empty() or slot.is_empty():
        return false

    # 槽位验证
    if not EQUIPMENT_SLOTS.has(slot):
        return false

    # 兼容性检查
    if not item_id.is_empty() and not _is_item_compatible(character_id, slot, item_id):
        return false

    # 库存检查
    if not item_id.is_empty() and not _check_item_availability(item_id):
        return false

    return true

func _check_item_availability(item_id: String) -> bool:
    """检查物品可用性"""
    if not is_instance_valid(resource_manager):
        return true  # 如果没有资源管理器，跳过检查

    return resource_manager.get_item_amount(item_id) > 0

func _execute_equipment_change(character_id: String, slot: String, old_item_id: String, new_item_id: String) -> bool:
    """执行装备变更"""
    var char_equipment = get_character_equipment(character_id)

    # 卸下旧装备
    if not old_item_id.is_empty():
        _return_item_to_inventory(old_item_id)

    # 装备新物品
    if not new_item_id.is_empty():
        if not _consume_item_from_inventory(new_item_id):
            return false

    # 更新装备状态
    char_equipment[slot] = new_item_id

    # 重新计算效果并发送信号
    _recalculate_character_effects(character_id)
    equipment_equipped.emit(character_id, slot, new_item_id)

    return true

func _consume_item_from_inventory(item_id: String) -> bool:
    """从库存消耗物品 - 使用IResourceSystem统一接口"""
    return IResourceSystem.remove_item(item_id, 1)

func _return_item_to_inventory(item_id: String) -> void:
    """将物品返回库存 - 使用IResourceSystem统一接口"""
    IResourceSystem.add_item(item_id, 1)

func unequip_item(character_id: String, slot: String) -> String:
    """卸下装备 - 统一接口"""
    if character_id.is_empty() or slot.is_empty():
        return ""

    var char_equipment = get_character_equipment(character_id)
    var old_item_id = char_equipment.get(slot, "")

    if old_item_id.is_empty():
        return ""

    # 执行卸装操作
    return _execute_unequip(character_id, slot, old_item_id)

func _execute_unequip(character_id: String, slot: String, item_id: String) -> String:
    """执行卸装操作"""
    var char_equipment = get_character_equipment(character_id)

    # 清空槽位
    char_equipment[slot] = ""

    # 返回物品到库存
    _return_item_to_inventory(item_id)

    # 重新计算效果并发送信号
    _recalculate_character_effects(character_id)
    equipment_unequipped.emit(character_id, slot, item_id)

    return item_id

func get_character_equipment(character_id: String) -> Dictionary:
    """获取角色装备配置"""
    if not character_equipment.has(character_id):
        # 尝试获取已知的角色类型
        var character_type = character_types.get(character_id, "")
        register_character(character_id, character_type)
    return character_equipment[character_id]

func get_equipped_item(character_id: String, slot: String) -> String:
    """获取角色指定槽位的装备"""
    var char_equipment = get_character_equipment(character_id)
    return char_equipment.get(slot, "")

func is_item_equipped(character_id: String, item_id: String) -> bool:
    """检查物品是否已装备"""
    var char_equipment = get_character_equipment(character_id)
    for slot in char_equipment:
        if char_equipment[slot] == item_id:
            return true
    return false

## 兼容性检查 ##
func _is_item_compatible(character_id: String, slot: String, item_id: String) -> bool:
    """检查物品是否与角色和槽位兼容"""
    if item_id.is_empty():
        return true  # 空物品（卸装）总是兼容的

    var item_config = get_equipment_item_config(item_id)
    if item_config.is_empty():
        push_warning("EquipmentManager: 物品 %s 不存在于装备配置中" % item_id)
        return false

    # 检查槽位兼容性
    # 优先使用 compatible_slots 字段，如果没有则使用 category 字段
    var compatible_slots = item_config.get("compatible_slots", [])
    if compatible_slots.is_empty():
        # 如果没有 compatible_slots，使用 category 字段
        var category = item_config.get("category", "")
        if category.is_empty() or category != slot:
            return false
    else:
        # 使用 compatible_slots 字段
        if not slot in compatible_slots:
            return false

    # 检查角色类型兼容性
    var character_type = character_types.get(character_id, "")
    var compatible_characters = item_config.get("compatible_characters", [])

    # 如果没有指定兼容角色，则所有角色都可以使用
    if compatible_characters.is_empty():
        return true

    if not character_type in compatible_characters:
        return false

    return true

## 效果计算 - 配置驱动 ##
func _recalculate_character_effects(character_id: String) -> void:
    """重新计算角色装备效果 - 配置驱动模式"""
    var char_equipment = get_character_equipment(character_id)
    var total_effects = {}

    # 遍历装备槽位，累积效果
    for slot in char_equipment:
        var item_id = char_equipment[slot]
        if item_id.is_empty():
            continue

        var item_effects = _get_item_effects(item_id)
        _merge_effects_into_total(total_effects, item_effects)

    # 缓存并通知
    _cache_and_notify_effects(character_id, total_effects)

func _get_item_effects(item_id: String) -> Dictionary:
    """获取物品效果 - 配置驱动"""
    var item_config = get_equipment_item_config(item_id)
    return item_config.get("effects", {})

func _merge_effects_into_total(total_effects: Dictionary, item_effects: Dictionary) -> void:
    """将物品效果合并到总效果中"""
    for effect_type in item_effects:
        var effect_value = item_effects[effect_type]
        _merge_effect_value(total_effects, effect_type, effect_value)

func _cache_and_notify_effects(character_id: String, total_effects: Dictionary) -> void:
    """缓存效果并发送通知"""
    character_effects_cache[character_id] = total_effects
    equipment_effects_changed.emit(character_id, total_effects)

func _merge_effect_value(total_effects: Dictionary, effect_type: String, effect_value) -> void:
    """合并效果值 - 配置驱动模式"""
    if not total_effects.has(effect_type):
        total_effects[effect_type] = effect_value
        return

    var existing_value = total_effects[effect_type]

    # 使用配置驱动的效果处理类型
    var process_type = EFFECT_TYPES.get(effect_type, GameConstants.EquipmentConstants.EffectProcessType.REPLACE)

    match process_type:
        GameConstants.EquipmentConstants.EffectProcessType.ADDITIVE:
            total_effects[effect_type] = _merge_additive_effect(existing_value, effect_value)

        GameConstants.EquipmentConstants.EffectProcessType.MULTIPLIER:
            total_effects[effect_type] = _merge_multiplier_effect(existing_value, effect_value)

        GameConstants.EquipmentConstants.EffectProcessType.ABSOLUTE:
            total_effects[effect_type] = _merge_absolute_effect(existing_value, effect_value)

        GameConstants.EquipmentConstants.EffectProcessType.RANGE:
            total_effects[effect_type] = _merge_range_effect(existing_value, effect_value)

        GameConstants.EquipmentConstants.EffectProcessType.BOOLEAN:
            total_effects[effect_type] = _merge_boolean_effect(existing_value, effect_value)

        GameConstants.EquipmentConstants.EffectProcessType.TIME_REDUCTION:
            total_effects[effect_type] = _merge_time_reduction_effect(existing_value, effect_value)

        GameConstants.EquipmentConstants.EffectProcessType.REPLACE, _:
            total_effects[effect_type] = effect_value

## 效果合并辅助方法 - 配置驱动 ##
func _merge_additive_effect(existing_value, new_value):
    """累加效果合并"""
    if _is_numeric(existing_value) and _is_numeric(new_value):
        return existing_value + new_value
    return new_value

func _merge_multiplier_effect(existing_value, new_value):
    """乘数效果合并"""
    if _is_numeric(existing_value) and _is_numeric(new_value):
        return existing_value * new_value
    return new_value

func _merge_absolute_effect(existing_value, new_value):
    """绝对值效果合并 - 取最大值"""
    if _is_numeric(existing_value) and _is_numeric(new_value):
        return max(existing_value, new_value)
    return new_value

func _merge_range_effect(existing_value, new_value):
    """范围效果合并 - 选择更好的范围"""
    if typeof(existing_value) != TYPE_STRING or typeof(new_value) != TYPE_STRING:
        return new_value

    var existing_max = _parse_range_max(existing_value)
    var new_max = _parse_range_max(new_value)
    return new_value if new_max > existing_max else existing_value

func _merge_boolean_effect(existing_value, new_value):
    """布尔效果合并 - 逻辑或"""
    if typeof(existing_value) == TYPE_BOOL and typeof(new_value) == TYPE_BOOL:
        return existing_value or new_value
    return new_value

func _merge_time_reduction_effect(existing_value, new_value):
    """时间减少效果合并 - 累加减少量"""
    if _is_numeric(existing_value) and _is_numeric(new_value):
        return min(existing_value + new_value, 1.0)  # 最大减少100%
    return new_value

func _is_numeric(value) -> bool:
    """检查值是否为数值类型"""
    return typeof(value) == TYPE_INT or typeof(value) == TYPE_FLOAT

func _parse_range_max(range_str: String) -> int:
    """解析范围字符串的最大值"""
    var parts = range_str.split("-")
    if parts.size() >= 2:
        return int(parts[1])
    return int(range_str) if range_str.is_valid_int() else 0

func get_character_total_effects(character_id: String) -> Dictionary:
    """获取角色的总装备效果"""
    if character_id.is_empty():
        return {}
    
    if not character_effects_cache.has(character_id):
        _recalculate_character_effects(character_id)
    return character_effects_cache.get(character_id, {})

## 数据查询 - 配置驱动接口 ##
func get_equipment_item_config(item_id: String) -> Dictionary:
    """获取装备物品配置"""
    if not _is_loaded or item_id.is_empty():
        return {}
    return equipment_config.get("equipment_items", {}).get(item_id, {})

func get_compatible_items_for_slot(character_type: String, slot: String) -> Array:
    """获取兼容装备列表 - 配置驱动"""
    if not _is_loaded or character_type.is_empty() or slot.is_empty():
        return []

    # 从配置中获取兼容性信息
    var compatibility = equipment_config.get("character_compatibility", {})
    return compatibility.get(character_type, {}).get(slot, [])

func get_all_equipment_items() -> Dictionary:
    """获取所有装备配置"""
    return equipment_config.get("equipment_items", {}) if _is_loaded else {}

func get_equipment_categories() -> Dictionary:
    """获取装备分类配置"""
    return equipment_config.get("equipment_categories", {}) if _is_loaded else {}

func get_ui_settings() -> Dictionary:
    """获取UI设置"""
    return equipment_config.get("ui_settings", {}) if _is_loaded else {}

## 效果描述 - 配置驱动 ##
func get_effect_description(effect_name: String) -> String:
    """获取效果描述 - 配置驱动回退机制"""
    if effect_name.is_empty():
        return ""

    # 优先从配置文件获取
    if _is_loaded:
        var descriptions = equipment_config.get("effect_descriptions", {})
        var description = descriptions.get(effect_name, "")
        if not description.is_empty():
            return description

    # 回退到常量定义
    return GameConstants.EquipmentConstants.DEFAULT_EFFECT_DESCRIPTIONS.get(effect_name, effect_name)

func generate_effects_summary(effects: Dictionary) -> String:
    """生成效果摘要 - 简化版本"""
    if effects.is_empty():
        return "无加成效果"

    var summary_parts: PackedStringArray = []
    for effect_name in effects:
        var effect_value = effects[effect_name]
        var description = get_effect_description(effect_name)
        var formatted_value = _format_effect_value(effect_value)
        summary_parts.append("%s%s" % [description, formatted_value])

    return " ".join(summary_parts)

func _format_effect_value(effect_value) -> String:
    """格式化效果值"""
    match typeof(effect_value):
        TYPE_STRING:
            return "+" + str(effect_value)
        TYPE_INT:
            return "+%d" % effect_value if effect_value != 0 else "0"
        TYPE_FLOAT:
            if effect_value == 0.0:
                return "0"
            elif effect_value < 1.0:
                return "+%d%%" % (effect_value * 100)
            else:
                return "+%.1f" % effect_value
        TYPE_DICTIONARY, TYPE_ARRAY:
            return "已配置"
        _:
            return str(effect_value)

## 工具方法 - 简化接口 ##
func get_item_icon_region_data(item_id: String) -> Dictionary:
    """获取物品图标数据 - 统一接口"""
    if item_id.is_empty() or not is_instance_valid(data_manager):
        return {}
    return data_manager.get_item_icon_region_data(item_id)

func has_item_in_inventory(item_id: String) -> bool:
    """检查库存可用性"""
    return _check_item_availability(item_id)

## 状态查询 ##
func is_loaded() -> bool:
    """系统加载状态"""
    return _is_loaded

func get_debug_info() -> Dictionary:
    """调试信息 - 配置驱动"""
    return {
        "is_loaded": _is_loaded,
        "config_path": CONFIG_PATH,
        "registered_characters": character_equipment.size(),
        "cached_effects": character_effects_cache.size(),
        "equipment_items": get_all_equipment_items().size(),
        "character_types": character_types.size()
    }

## 角色管理 - 简化版本 ##
func clear_character_equipment(character_id: String) -> void:
    """清除角色装备 - 统一清理"""
    if character_id.is_empty():
        return

    # 卸下所有装备
    var char_equipment = get_character_equipment(character_id)
    for slot in EQUIPMENT_SLOTS:
        var item_id = char_equipment.get(slot, "")
        if not item_id.is_empty():
            unequip_item(character_id, slot)

    # 清理数据
    unregister_character(character_id)

func register_character_type(character_id: String, character_type: String) -> void:
    """注册角色类型 - 统一接口"""
    if character_id.is_empty() or character_type.is_empty():
        return

    # 确保角色已注册到装备系统
    if not character_equipment.has(character_id):
        register_character(character_id, character_type)
    else:
        character_types[character_id] = character_type

func get_character_type(character_id: String) -> String:
    """获取角色类型"""
    return character_types.get(character_id, "")
